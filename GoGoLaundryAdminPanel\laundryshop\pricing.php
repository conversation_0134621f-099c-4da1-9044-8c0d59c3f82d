<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Pricing Management';
$currentPage = 'pricing';

// Handle pricing updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_item_price':
            $itemId = intval($_POST['item_id']);
            $customPrice = !empty($_POST['custom_price']) ? floatval($_POST['custom_price']) : null;
            
            try {
                // Check if shop item exists
                $checkStmt = $pdo->prepare("SELECT id FROM shop_items WHERE shop_id = ? AND item_id = ?");
                $checkStmt->execute([$shopOwnerData['shop_id'], $itemId]);
                
                if ($checkStmt->fetch()) {
                    // Update existing
                    $stmt = $pdo->prepare("UPDATE shop_items SET custom_price = ? WHERE shop_id = ? AND item_id = ?");
                    $stmt->execute([$customPrice, $shopOwnerData['shop_id'], $itemId]);
                } else {
                    // Insert new (make item available with custom price)
                    $stmt = $pdo->prepare("INSERT INTO shop_items (shop_id, item_id, custom_price, is_available) VALUES (?, ?, ?, 1)");
                    $stmt->execute([$shopOwnerData['shop_id'], $itemId, $customPrice]);
                }
                
                $_SESSION['shop_success_message'] = 'Item price updated successfully!';
            } catch (PDOException $e) {
                error_log('Pricing update error: ' . $e->getMessage());
                $_SESSION['shop_error_message'] = 'Failed to update item price.';
            }
            break;
            
        case 'bulk_price_update':
            $priceMultiplier = floatval($_POST['price_multiplier']);
            $serviceId = !empty($_POST['service_id']) ? intval($_POST['service_id']) : null;
            
            if ($priceMultiplier > 0 && $priceMultiplier <= 5) {
                try {
                    $whereClause = "WHERE si.shop_id = ?";
                    $params = [$shopOwnerData['shop_id']];
                    
                    if ($serviceId) {
                        $whereClause .= " AND i.service_id = ?";
                        $params[] = $serviceId;
                    }
                    
                    $sql = "
                        UPDATE shop_items si
                        JOIN items i ON si.item_id = i.id
                        SET si.custom_price = ROUND(i.price * ?, 2)
                        $whereClause
                    ";
                    
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute(array_merge([$priceMultiplier], $params));
                    
                    $_SESSION['shop_success_message'] = 'Bulk price update completed successfully!';
                } catch (PDOException $e) {
                    error_log('Bulk pricing update error: ' . $e->getMessage());
                    $_SESSION['shop_error_message'] = 'Failed to update prices.';
                }
            } else {
                $_SESSION['shop_error_message'] = 'Invalid price multiplier. Must be between 0.1 and 5.0.';
            }
            break;
    }
}

// Get all items with pricing information
try {
    $itemsStmt = $pdo->prepare("
        SELECT i.*, s.name as service_name, s.id as service_id,
               si.custom_price, si.is_available as shop_is_available,
               COALESCE(si.custom_price, i.price) as effective_price
        FROM items i
        LEFT JOIN services s ON i.service_id = s.id
        LEFT JOIN shop_items si ON i.id = si.item_id AND si.shop_id = ?
        WHERE i.is_active = 1 AND s.is_active = 1
        ORDER BY s.name ASC, i.name ASC
    ");
    $itemsStmt->execute([$shopOwnerData['shop_id']]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Items pricing fetch error: ' . $e->getMessage());
    $items = [];
}

// Get services for bulk update
try {
    $servicesStmt = $pdo->prepare("SELECT id, name FROM services WHERE is_active = 1 ORDER BY name ASC");
    $servicesStmt->execute();
    $services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $services = [];
}

// Group items by service
$itemsByService = [];
foreach ($items as $item) {
    $itemsByService[$item['service_name']][] = $item;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Pricing Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
            <i class="fas fa-calculator me-1"></i>Bulk Price Update
        </button>
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<!-- Pricing Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Items</div>
                        <div class="h5 mb-0 font-weight-bold"><?= count($items) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tshirt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Custom Priced</div>
                        <div class="h5 mb-0 font-weight-bold">
                            <?= count(array_filter($items, function($item) { return $item['custom_price'] !== null; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Available Items</div>
                        <div class="h5 mb-0 font-weight-bold">
                            <?= count(array_filter($items, function($item) { return $item['shop_is_available'] == 1; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Avg. Price</div>
                        <div class="h5 mb-0 font-weight-bold">
                            ৳<?= $items ? number_format(array_sum(array_column($items, 'effective_price')) / count($items), 2) : '0.00' ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Items Pricing by Service -->
<?php if (empty($itemsByService)): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
            <p class="text-muted">No items available for pricing.</p>
        </div>
    </div>
<?php else: ?>
    <?php foreach ($itemsByService as $serviceName => $serviceItems): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-concierge-bell me-2"></i><?= htmlspecialchars($serviceName) ?>
                </h5>
                <small class="text-muted">Manage pricing for <?= count($serviceItems) ?> items</small>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Default Price</th>
                                <th>Your Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($serviceItems as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($item['image_url']): ?>
                                                <img src="<?= htmlspecialchars($item['image_url']) ?>" 
                                                     class="item-image me-2" alt="Item">
                                            <?php endif; ?>
                                            <div>
                                                <strong><?= htmlspecialchars($item['name']) ?></strong>
                                                <?php if ($item['bn_name']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($item['bn_name']) ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">৳<?= number_format($item['price'], 2) ?></span>
                                    </td>
                                    <td>
                                        <?php if ($item['custom_price']): ?>
                                            <span class="badge bg-primary">৳<?= number_format($item['custom_price'], 2) ?></span>
                                            <?php
                                            $difference = (($item['custom_price'] - $item['price']) / $item['price']) * 100;
                                            $diffClass = $difference > 0 ? 'text-success' : 'text-danger';
                                            ?>
                                            <br><small class="<?= $diffClass ?>">
                                                <?= $difference > 0 ? '+' : '' ?><?= number_format($difference, 1) ?>%
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">Using default</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($item['shop_is_available']): ?>
                                            <span class="badge bg-success">Available</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Not Available</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="editItemPrice(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>', <?= $item['price'] ?>, <?= $item['custom_price'] ?: 'null' ?>)">
                                            <i class="fas fa-edit"></i> Edit Price
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<!-- Edit Item Price Modal -->
<div class="modal fade" id="editItemPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Item Price</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_item_price">
                    <input type="hidden" name="item_id" id="edit_item_id">
                    
                    <div class="alert alert-info">
                        <strong id="edit_item_name"></strong>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Price</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="text" class="form-control" id="edit_default_price" readonly>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_custom_price" class="form-label">Your Custom Price</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="number" class="form-control" id="edit_custom_price" name="custom_price" 
                                   step="0.01" min="0" placeholder="Leave empty to use default price">
                        </div>
                        <div class="form-text">Set a custom price for this item, or leave empty to use the default price.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Price</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Price Update Modal -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Price Update</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="bulk_price_update">
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will update custom prices for all selected items based on their default prices.
                    </div>
                    
                    <div class="mb-3">
                        <label for="service_id" class="form-label">Service (Optional)</label>
                        <select class="form-select" id="service_id" name="service_id">
                            <option value="">All Services</option>
                            <?php foreach ($services as $service): ?>
                                <option value="<?= $service['id'] ?>"><?= htmlspecialchars($service['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Leave empty to update all services</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="price_multiplier" class="form-label">Price Multiplier</label>
                        <input type="number" class="form-control" id="price_multiplier" name="price_multiplier" 
                               step="0.1" min="0.1" max="5.0" value="1.0" required>
                        <div class="form-text">
                            Examples: 1.2 = +20%, 0.9 = -10%, 1.0 = no change
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Prices</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }
.item-image { width: 40px; height: 40px; object-fit: cover; border-radius: 4px; }
</style>

<script>
function editItemPrice(itemId, itemName, defaultPrice, customPrice) {
    document.getElementById('edit_item_id').value = itemId;
    document.getElementById('edit_item_name').textContent = itemName;
    document.getElementById('edit_default_price').value = defaultPrice.toFixed(2);
    document.getElementById('edit_custom_price').value = customPrice ? customPrice.toFixed(2) : '';
    
    new bootstrap.Modal(document.getElementById('editItemPriceModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
