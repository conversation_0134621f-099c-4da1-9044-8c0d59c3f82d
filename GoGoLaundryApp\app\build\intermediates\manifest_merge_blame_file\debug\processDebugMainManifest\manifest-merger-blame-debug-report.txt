1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdsadrulhasan.gogolaundry"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12    <uses-feature
12-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:6:5-8:36
13        android:name="android.hardware.camera"
13-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:7:9-47
14        android:required="false" />
14-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:8:9-33
15
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:10:5-67
16-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:11:22-76
18    <uses-permission
18-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:12:5-107
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:12:22-77
20        android:maxSdkVersion="32" />
20-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:12:78-104
21    <uses-permission
21-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:5-108
22        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
22-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:22-78
23        android:maxSdkVersion="28" />
23-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:79-105
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:14:5-76
24-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:14:22-73
25    <uses-permission android:name="android.permission.CAMERA" />
25-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:15:5-65
25-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:15:22-62
26    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
26-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:16:5-77
26-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:16:22-74
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:17:5-68
27-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:17:22-65
28    <uses-permission android:name="android.permission.VIBRATE" />
28-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:18:5-66
28-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:18:22-63
29
30    <!-- Location permissions for map functionality -->
31    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
31-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:21:5-79
31-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:21:22-76
32    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
32-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:22:5-81
32-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:22:22-78
33
34    <!-- OSMDroid permissions -->
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:11:5-79
35-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:11:22-76
36    <uses-permission
36-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:5-108
37        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
37-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:22-78
38        android:maxSdkVersion="28" />
38-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:79-105
39
40    <uses-feature
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
41        android:glEsVersion="0x00020000"
41-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
42        android:required="true" />
42-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
43
44    <queries>
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
45
46        <!-- Needs to be explicitly declared on Android R+ -->
47        <package android:name="com.google.android.apps.maps" />
47-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
47-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
48    </queries> <!-- Required by older versions of Google Play services to create IID tokens -->
49    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
49-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
49-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
50    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
50-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
50-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
51    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
51-->[com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:5-79
51-->[com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:22-76
52    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
52-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
52-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
53    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
53-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
53-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
54
55    <permission
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
56        android:name="com.mdsadrulhasan.gogolaundry.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
57        android:protectionLevel="signature" />
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
58
59    <uses-permission android:name="com.mdsadrulhasan.gogolaundry.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
60
61    <supports-screens
61-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:9:5-12:40
62        android:anyDensity="true"
62-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:10:9-34
63        android:largeScreens="true"
63-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:11:9-36
64        android:normalScreens="true" />
64-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:12:9-37
65
66    <uses-feature
66-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:14:5-16:36
67        android:name="android.hardware.location.network"
67-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:15:9-57
68        android:required="false" />
68-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:16:9-33
69    <uses-feature
69-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:17:5-19:36
70        android:name="android.hardware.location.gps"
70-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:18:9-53
71        android:required="false" />
71-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:19:9-33
72    <uses-feature
72-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:20:5-22:36
73        android:name="android.hardware.telephony"
73-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:21:9-50
74        android:required="false" />
74-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:22:9-33
75    <uses-feature
75-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:23:5-25:36
76        android:name="android.hardware.wifi"
76-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:24:9-45
77        android:required="false" />
77-->[org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:25:9-33
78
79    <application
79-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:29:5-100:19
80        android:name="com.mdsadrulhasan.gogolaundry.GoGoLaundryApp"
80-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:30:9-39
81        android:allowBackup="true"
81-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:32:9-35
82        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
83        android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
83-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:40:9-83
84        android:dataExtractionRules="@xml/data_extraction_rules"
84-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:33:9-65
85        android:debuggable="true"
86        android:extractNativeLibs="false"
87        android:fullBackupContent="@xml/backup_rules"
87-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:34:9-54
88        android:icon="@mipmap/ic_launcher"
88-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:35:9-43
89        android:label="@string/app_name"
89-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:36:9-41
90        android:roundIcon="@mipmap/ic_launcher_round"
90-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:37:9-54
91        android:supportsRtl="true"
91-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:38:9-35
92        android:testOnly="true"
93        android:theme="@style/Theme.GoGoLaundry"
93-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:39:9-49
94        android:usesCleartextTraffic="true" >
94-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:31:9-44
95
96        <!-- Login Activity -->
97        <activity
97-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:44:9-51:20
98            android:name="com.mdsadrulhasan.gogolaundry.LoginActivity"
98-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:45:13-42
99            android:exported="true" >
99-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:46:13-36
100            <intent-filter>
100-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:47:13-50:29
101                <action android:name="android.intent.action.MAIN" />
101-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:48:17-69
101-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:48:25-66
102
103                <category android:name="android.intent.category.LAUNCHER" />
103-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:49:17-77
103-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:49:27-74
104            </intent-filter>
105        </activity>
106
107        <!-- Signup Activity -->
108        <activity
108-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:54:9-56:40
109            android:name="com.mdsadrulhasan.gogolaundry.SignupActivity"
109-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:55:13-43
110            android:exported="false" />
110-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:56:13-37
111
112        <!-- Forgot Password Activity -->
113        <activity
113-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:59:9-61:40
114            android:name="com.mdsadrulhasan.gogolaundry.ForgotPasswordActivity"
114-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:60:13-51
115            android:exported="false" />
115-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:61:13-37
116
117        <!-- Main Activity -->
118        <activity
118-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:64:9-67:90
119            android:name="com.mdsadrulhasan.gogolaundry.MainActivity"
119-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:65:13-41
120            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
120-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:67:13-87
121            android:exported="false" />
121-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:66:13-37
122
123        <!-- File Provider for profile pictures -->
124        <provider
124-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:71:9-79:20
125            android:name="androidx.core.content.FileProvider"
125-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:72:13-62
126            android:authorities="com.mdsadrulhasan.gogolaundry.fileprovider"
126-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:73:13-77
127            android:exported="false"
127-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:74:13-37
128            android:grantUriPermissions="true" >
128-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:75:13-47
129            <meta-data
129-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:76:13-78:54
130                android:name="android.support.FILE_PROVIDER_PATHS"
130-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:77:17-67
131                android:resource="@xml/file_paths" />
131-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:78:17-51
132        </provider>
133
134        <!-- Firebase Cloud Messaging Service -->
135        <service
135-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:82:9-88:19
136            android:name="com.mdsadrulhasan.gogolaundry.fcm.GoGoLaundryFirebaseMessagingService"
136-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:83:13-68
137            android:exported="false" >
137-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:84:13-37
138            <intent-filter>
138-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:85:13-87:29
139                <action android:name="com.google.firebase.MESSAGING_EVENT" />
139-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:86:17-78
139-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:86:25-75
140            </intent-filter>
141        </service>
142
143        <!-- Default notification icon and color -->
144        <meta-data
144-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:91:9-93:60
145            android:name="com.google.firebase.messaging.default_notification_icon"
145-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:92:13-83
146            android:resource="@drawable/ic_notification" />
146-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:93:13-57
147        <meta-data
147-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:94:9-96:54
148            android:name="com.google.firebase.messaging.default_notification_color"
148-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:95:13-84
149            android:resource="@color/colorPrimary" />
149-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:96:13-51
150        <meta-data
150-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:97:9-99:57
151            android:name="com.google.firebase.messaging.default_notification_channel_id"
151-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:98:13-89
152            android:value="gogolaundry_notifications" />
152-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:99:13-54
153        <!-- Needs to be explicitly declared on P+ -->
154        <uses-library
154-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
155            android:name="org.apache.http.legacy"
155-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
156            android:required="false" />
156-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
157
158        <receiver
158-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
159            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
159-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
160            android:exported="true"
160-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
161            android:permission="com.google.android.c2dm.permission.SEND" >
161-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
162            <intent-filter>
162-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
163                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
163-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
163-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
164            </intent-filter>
165
166            <meta-data
166-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
167                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
167-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
168                android:value="true" />
168-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
169        </receiver>
170        <!--
171             FirebaseMessagingService performs security checks at runtime,
172             but set to not exported to explicitly avoid allowing another app to call it.
173        -->
174        <service
174-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
175            android:name="com.google.firebase.messaging.FirebaseMessagingService"
175-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
176            android:directBootAware="true"
176-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
177            android:exported="false" >
177-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
178            <intent-filter android:priority="-500" >
178-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:85:13-87:29
179                <action android:name="com.google.firebase.MESSAGING_EVENT" />
179-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:86:17-78
179-->C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:86:25-75
180            </intent-filter>
181        </service>
182        <service
182-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
183            android:name="com.google.firebase.components.ComponentDiscoveryService"
183-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:55:13-84
184            android:directBootAware="true"
184-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
185            android:exported="false" >
185-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
186            <meta-data
186-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
187                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
187-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
189            <meta-data
189-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
190                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
190-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
192            <meta-data
192-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
193                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
193-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
194                android:value="com.google.firebase.components.ComponentRegistrar" />
194-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
195            <meta-data
195-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
196                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
196-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
198            <meta-data
198-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
199                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
199-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
201            <meta-data
201-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
202                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
202-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
204            <meta-data
204-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
205                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
205-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
207            <meta-data
207-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
208                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
208-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
210        </service>
211
212        <activity
212-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
213            android:name="com.google.android.gms.common.api.GoogleApiActivity"
213-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
214            android:exported="false"
214-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
215            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
215-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
216
217        <receiver
217-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
218            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
218-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
219            android:enabled="true"
219-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
220            android:exported="false" >
220-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
221        </receiver>
222
223        <service
223-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
224            android:name="com.google.android.gms.measurement.AppMeasurementService"
224-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
225            android:enabled="true"
225-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
226            android:exported="false" />
226-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
227        <service
227-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
228            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
228-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
229            android:enabled="true"
229-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
230            android:exported="false"
230-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
231            android:permission="android.permission.BIND_JOB_SERVICE" />
231-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
232
233        <property
233-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
234            android:name="android.adservices.AD_SERVICES_CONFIG"
234-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
235            android:resource="@xml/ga_ad_services_config" />
235-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
236
237        <provider
237-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
238            android:name="com.google.firebase.provider.FirebaseInitProvider"
238-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
239            android:authorities="com.mdsadrulhasan.gogolaundry.firebaseinitprovider"
239-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
240            android:directBootAware="true"
240-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
241            android:exported="false"
241-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
242            android:initOrder="100" />
242-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
243
244        <service
244-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
245            android:name="androidx.room.MultiInstanceInvalidationService"
245-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
246            android:directBootAware="true"
246-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
247            android:exported="false" />
247-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
248
249        <provider
249-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
250            android:name="androidx.startup.InitializationProvider"
250-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
251            android:authorities="com.mdsadrulhasan.gogolaundry.androidx-startup"
251-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
252            android:exported="false" >
252-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
253            <meta-data
253-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
254                android:name="androidx.emoji2.text.EmojiCompatInitializer"
254-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
255                android:value="androidx.startup" />
255-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
256            <meta-data
256-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
257                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
257-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
258                android:value="androidx.startup" />
258-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
259            <meta-data
259-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
260                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
260-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
261                android:value="androidx.startup" />
261-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
262        </provider>
263
264        <uses-library
264-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
265            android:name="android.ext.adservices"
265-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
266            android:required="false" />
266-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
267
268        <meta-data
268-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
269            android:name="com.google.android.gms.version"
269-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
270            android:value="@integer/google_play_services_version" />
270-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
271
272        <service
272-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
273            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
273-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
274            android:exported="false" >
274-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
275            <meta-data
275-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
276                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
276-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
277                android:value="cct" />
277-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
278        </service>
279        <service
279-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
280            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
280-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
281            android:exported="false"
281-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
282            android:permission="android.permission.BIND_JOB_SERVICE" >
282-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
283        </service>
284
285        <receiver
285-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
286            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
286-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
287            android:exported="false" />
287-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
288        <receiver
288-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
289            android:name="androidx.profileinstaller.ProfileInstallReceiver"
289-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
290            android:directBootAware="false"
290-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
291            android:enabled="true"
291-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
292            android:exported="true"
292-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
293            android:permission="android.permission.DUMP" >
293-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
294            <intent-filter>
294-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
295                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
295-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
295-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
296            </intent-filter>
297            <intent-filter>
297-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
298                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
298-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
298-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
299            </intent-filter>
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
301                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
302            </intent-filter>
303            <intent-filter>
303-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
304                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
305            </intent-filter>
306        </receiver>
307    </application>
308
309</manifest>
