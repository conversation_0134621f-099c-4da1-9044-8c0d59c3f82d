            </div>
        </div>
    </div>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Initialize DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Confirm delete actions
            $('.delete-confirm').on('click', function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });

            // Form validation
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.prop('disabled', true);
                submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');
                
                // Re-enable button after 10 seconds to prevent permanent disable
                setTimeout(function() {
                    submitBtn.prop('disabled', false);
                    submitBtn.html(submitBtn.data('original-text') || 'Submit');
                }, 10000);
            });

            // Store original button text
            $('button[type="submit"]').each(function() {
                $(this).data('original-text', $(this).html());
            });

            // Sidebar toggle for mobile
            $('#sidebarCollapse').on('click', function() {
                $('.sidebar').toggleClass('active');
                $('.content').toggleClass('active');
            });

            // Close sidebar on mobile when clicking outside
            $(document).on('click', function(e) {
                if ($(window).width() <= 768) {
                    if (!$(e.target).closest('.sidebar, #sidebarCollapse').length) {
                        $('.sidebar').removeClass('active');
                        $('.content').removeClass('active');
                    }
                }
            });

            // Handle window resize
            $(window).on('resize', function() {
                if ($(window).width() > 768) {
                    $('.sidebar').removeClass('active');
                    $('.content').removeClass('active');
                }
            });

            // Tooltip initialization
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Popover initialization
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Number formatting
            $('.format-number').each(function() {
                const value = parseFloat($(this).text());
                if (!isNaN(value)) {
                    $(this).text(value.toLocaleString());
                }
            });

            // Currency formatting
            $('.format-currency').each(function() {
                const value = parseFloat($(this).text());
                if (!isNaN(value)) {
                    $(this).text('৳' + value.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                }
            });

            // Auto-refresh for real-time data (every 5 minutes)
            if (typeof autoRefresh !== 'undefined' && autoRefresh) {
                setInterval(function() {
                    location.reload();
                }, 300000); // 5 minutes
            }

            // Print functionality
            $('.print-btn').on('click', function() {
                window.print();
            });

            // Export functionality
            $('.export-btn').on('click', function() {
                const table = $('.data-table').DataTable();
                const data = table.buttons.exportData();
                // Add export logic here
            });

            // Status update functionality
            $('.status-toggle').on('change', function() {
                const checkbox = $(this);
                const itemId = checkbox.data('id');
                const itemType = checkbox.data('type');
                const isChecked = checkbox.is(':checked');
                
                $.ajax({
                    url: 'ajax/update_status.php',
                    method: 'POST',
                    data: {
                        id: itemId,
                        type: itemType,
                        status: isChecked ? 1 : 0
                    },
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', 'Status updated successfully');
                        } else {
                            showAlert('error', response.message || 'Failed to update status');
                            checkbox.prop('checked', !isChecked); // Revert checkbox
                        }
                    },
                    error: function() {
                        showAlert('error', 'Network error occurred');
                        checkbox.prop('checked', !isChecked); // Revert checkbox
                    }
                });
            });

            // Show alert function
            function showAlert(type, message) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
                
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="fas ${iconClass} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                
                $('.container-fluid').prepend(alertHtml);
                
                // Auto-hide after 3 seconds
                setTimeout(function() {
                    $('.alert').first().fadeOut('slow');
                }, 3000);
            }

            // Image preview functionality
            $('.image-input').on('change', function() {
                const input = this;
                const preview = $(input).siblings('.image-preview');
                
                if (input.files && input.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        preview.attr('src', e.target.result).show();
                    };
                    
                    reader.readAsDataURL(input.files[0]);
                }
            });

            // Copy to clipboard functionality
            $('.copy-btn').on('click', function() {
                const text = $(this).data('copy');
                navigator.clipboard.writeText(text).then(function() {
                    showAlert('success', 'Copied to clipboard');
                });
            });
        });

        // Global functions
        function confirmAction(message) {
            return confirm(message || 'Are you sure you want to perform this action?');
        }

        function formatCurrency(amount) {
            return '৳' + parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function formatNumber(number) {
            return parseFloat(number).toLocaleString();
        }

        function showLoading(element) {
            $(element).html('<i class="fas fa-spinner fa-spin"></i> Loading...');
        }

        function hideLoading(element, originalText) {
            $(element).html(originalText);
        }
    </script>

    <!-- Page-specific scripts -->
    <?php if (isset($pageScripts)): ?>
        <?php foreach ($pageScripts as $script): ?>
            <script src="<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline scripts -->
    <?php if (isset($inlineScripts)): ?>
        <script>
            <?php echo $inlineScripts; ?>
        </script>
    <?php endif; ?>

</body>
</html>
