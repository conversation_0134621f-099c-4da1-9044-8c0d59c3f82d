<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Dashboard';
$currentPage = 'dashboard';

// Get shop statistics
try {
    // Get total orders for this shop
    $orderStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'placed' THEN 1 END) as new_orders,
            COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total ELSE 0 END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN status = 'delivered' AND DATE(created_at) = CURDATE() THEN total ELSE 0 END), 0) as today_revenue
        FROM orders 
        WHERE shop_id = ?
    ");
    $orderStmt->execute([$shopOwnerData['shop_id']]);
    $stats = $orderStmt->fetch(PDO::FETCH_ASSOC);

    // Get recent orders
    $recentOrdersStmt = $pdo->prepare("
        SELECT o.*, u.full_name as customer_name, u.phone as customer_phone
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.shop_id = ?
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $recentOrdersStmt->execute([$shopOwnerData['shop_id']]);
    $recentOrders = $recentOrdersStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get shop services count
    $servicesStmt = $pdo->prepare("
        SELECT COUNT(*) as total_services
        FROM shop_services ss
        WHERE ss.shop_id = ? AND ss.is_available = 1
    ");
    $servicesStmt->execute([$shopOwnerData['shop_id']]);
    $servicesCount = $servicesStmt->fetchColumn();

    // Get shop items count
    $itemsStmt = $pdo->prepare("
        SELECT COUNT(*) as total_items
        FROM shop_items si
        WHERE si.shop_id = ? AND si.is_available = 1
    ");
    $itemsStmt->execute([$shopOwnerData['shop_id']]);
    $itemsCount = $itemsStmt->fetchColumn();

} catch (PDOException $e) {
    error_log('Dashboard stats error: ' . $e->getMessage());
    $stats = [
        'total_orders' => 0,
        'new_orders' => 0,
        'processing_orders' => 0,
        'completed_orders' => 0,
        'total_revenue' => 0,
        'today_revenue' => 0
    ];
    $recentOrders = [];
    $servicesCount = 0;
    $itemsCount = 0;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-primary fs-6">
                <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
            </span>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Orders
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['total_orders']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Revenue
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            ৳<?php echo number_format($stats['total_revenue'], 2); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            New Orders
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['new_orders']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Today's Revenue
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            ৳<?php echo number_format($stats['today_revenue'], 2); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="orders.php" class="btn btn-primary btn-block">
                            <i class="fas fa-shopping-cart me-2"></i>View Orders
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="services.php" class="btn btn-success btn-block">
                            <i class="fas fa-concierge-bell me-2"></i>Manage Services
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="items.php" class="btn btn-info btn-block">
                            <i class="fas fa-tshirt me-2"></i>Manage Items
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="profile.php" class="btn btn-warning btn-block">
                            <i class="fas fa-store me-2"></i>Shop Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Shop Overview</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="h4 mb-0 text-primary"><?php echo $servicesCount; ?></div>
                        <div class="small text-muted">Active Services</div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="h4 mb-0 text-success"><?php echo $itemsCount; ?></div>
                        <div class="small text-muted">Available Items</div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="h4 mb-0 text-info"><?php echo $stats['processing_orders']; ?></div>
                        <div class="small text-muted">Processing</div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="h4 mb-0 text-warning"><?php echo $stats['completed_orders']; ?></div>
                        <div class="small text-muted">Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
        <a href="orders.php" class="btn btn-primary btn-sm">View All</a>
    </div>
    <div class="card-body">
        <?php if (empty($recentOrders)): ?>
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <p class="text-muted">No orders yet.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Status</th>
                            <th>Total</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentOrders as $order): ?>
                            <tr>
                                <td>#<?php echo $order['id']; ?></td>
                                <td>
                                    <?php echo htmlspecialchars($order['customer_name'] ?? 'N/A'); ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_phone'] ?? ''); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    switch ($order['status']) {
                                        case 'placed': $statusClass = 'bg-warning'; break;
                                        case 'processing': $statusClass = 'bg-info'; break;
                                        case 'out_for_delivery': $statusClass = 'bg-primary'; break;
                                        case 'delivered': $statusClass = 'bg-success'; break;
                                        case 'cancelled': $statusClass = 'bg-danger'; break;
                                        default: $statusClass = 'bg-secondary';
                                    }
                                    ?>
                                    <span class="badge <?php echo $statusClass; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                    </span>
                                </td>
                                <td>৳<?php echo number_format($order['total'], 2); ?></td>
                                <td><?php echo date('M j, Y', strtotime($order['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.btn-block {
    width: 100%;
}
</style>

<?php include 'includes/footer.php'; ?>
