<?php
/**
 * Shop Owner Login API Endpoint
 *
 * This endpoint handles shop owner login
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ShopOwnerManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Validate input
if (empty($inputData['username'])) {
    jsonResponse(false, 'Username is required', [], 400);
}

if (empty($inputData['password'])) {
    jsonResponse(false, 'Password is required', [], 400);
}

$username = sanitize($inputData['username']);
$password = $inputData['password'];

try {
    // Initialize shop owner manager
    $shopOwnerManager = new ShopOwnerManager($pdo);

    // Authenticate shop owner
    $result = $shopOwnerManager->login($username, $password);

    if ($result === 'locked') {
        jsonResponse(false, 'Account is temporarily locked due to too many failed login attempts. Please try again later.', [], 423);
    } elseif ($result === false) {
        jsonResponse(false, 'Invalid username or password', [], 401);
    }

    // Check if shop owner account is active
    if (!$result['is_active']) {
        jsonResponse(false, 'Your account has been deactivated. Please contact support.', [], 403);
    }

    // Get shop details
    $shopStmt = $pdo->prepare("
        SELECT ls.*, 
               d.name as division_name,
               dist.name as district_name,
               up.name as upazilla_name
        FROM laundry_shops ls
        LEFT JOIN divisions d ON ls.division_id = d.id
        LEFT JOIN districts dist ON ls.district_id = dist.id
        LEFT JOIN upazillas up ON ls.upazilla_id = up.id
        WHERE ls.id = ?
    ");
    $shopStmt->execute([$result['shop_id']]);
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);

    if (!$shop) {
        jsonResponse(false, 'Shop not found', [], 404);
    }

    // Prepare response data
    $responseData = [
        'shop_owner' => [
            'id' => $result['id'],
            'username' => $result['username'],
            'email' => $result['email'],
            'full_name' => $result['full_name'],
            'phone' => $result['phone'],
            'is_active' => $result['is_active'],
            'is_verified' => $result['is_verified'],
            'last_login' => $result['last_login']
        ],
        'shop' => [
            'id' => $shop['id'],
            'name' => $shop['name'],
            'bn_name' => $shop['bn_name'],
            'description' => $shop['description'],
            'bn_description' => $shop['bn_description'],
            'owner_name' => $shop['owner_name'],
            'phone' => $shop['phone'],
            'email' => $shop['email'],
            'address' => $shop['address'],
            'division_id' => $shop['division_id'],
            'district_id' => $shop['district_id'],
            'upazilla_id' => $shop['upazilla_id'],
            'division_name' => $shop['division_name'],
            'district_name' => $shop['district_name'],
            'upazilla_name' => $shop['upazilla_name'],
            'latitude' => $shop['latitude'],
            'longitude' => $shop['longitude'],
            'operating_hours' => $shop['operating_hours'],
            'rating' => $shop['rating'],
            'total_reviews' => $shop['total_reviews'],
            'is_active' => $shop['is_active'],
            'is_verified' => $shop['is_verified'],
            'profile_image_url' => $shop['profile_image_url'],
            'cover_image_url' => $shop['cover_image_url'],
            'created_at' => $shop['created_at'],
            'updated_at' => $shop['updated_at']
        ]
    ];

    // Check shop status and provide appropriate message
    $message = 'Login successful';
    $status_info = [];

    if (!$shop['is_verified']) {
        $status_info['verification_status'] = 'pending';
        $status_info['message'] = 'Your shop is pending admin approval. You will be notified once verified.';
    } elseif (!$shop['is_active']) {
        $status_info['verification_status'] = 'inactive';
        $status_info['message'] = 'Your shop is currently inactive. Please contact support.';
    } else {
        $status_info['verification_status'] = 'active';
        $status_info['message'] = 'Welcome to your shop dashboard!';
    }

    $responseData['status_info'] = $status_info;

    // Set session for web interface (if needed)
    $_SESSION['shop_owner_id'] = $result['id'];
    $_SESSION['shop_owner_logged_in'] = true;
    $_SESSION['shop_owner_username'] = $result['username'];
    $_SESSION['shop_owner_shop_id'] = $result['shop_id'];

    // Log successful login
    error_log("Shop owner login successful: Username {$username}, Shop ID {$result['shop_id']}");

    // Return success response
    jsonResponse(true, $message, $responseData);

} catch (PDOException $e) {
    error_log('Shop owner login error: ' . $e->getMessage());
    jsonResponse(false, 'Login failed. Please try again later.', [], 500);
} catch (Exception $e) {
    error_log('Shop owner login error: ' . $e->getMessage());
    jsonResponse(false, 'Login failed. Please try again later.', [], 500);
}
