<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title><?php echo $pageTitle ?? 'Shop Dashboard'; ?> - <?php echo APP_NAME; ?> Shop Panel</title>

    <!-- jQuery (load before Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/admin.css">

    <!-- Shop Panel Custom Styles -->
    <style>
        :root {
            --shop-primary: #667eea;
            --shop-secondary: #764ba2;
            --shop-success: #28a745;
            --shop-info: #17a2b8;
            --shop-warning: #ffc107;
            --shop-danger: #dc3545;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--shop-primary) 0%, var(--shop-secondary) 100%);
        }

        .navbar {
            background: linear-gradient(135deg, var(--shop-primary) 0%, var(--shop-secondary) 100%) !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--shop-primary) 0%, var(--shop-secondary) 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--shop-secondary) 0%, var(--shop-primary) 100%);
            transform: translateY(-1px);
        }

        .card {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--shop-primary) 0%, var(--shop-secondary) 100%);
            color: white;
            border-bottom: none;
        }

        .sidebar .sidebar-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar ul li a {
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .sidebar ul li a:hover,
        .sidebar ul li.active a {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateX(5px);
        }

        .sidebar ul li a i {
            width: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            #sidebarCollapse {
                padding: 12px;
                margin-right: 10px;
                z-index: 1060;
                cursor: pointer;
                -webkit-tap-highlight-color: transparent;
            }

            .dropdown-menu {
                position: absolute !important;
                transform: none !important;
            }

            .navbar .container-fluid {
                padding-left: 5px;
                padding-right: 5px;
            }

            .sidebar ul li a {
                padding: 15px 20px;
                -webkit-tap-highlight-color: rgba(0,0,0,0.1);
            }

            .dropdown-menu.show {
                display: block !important;
            }

            .sidebar, .navbar, .btn, .nav-link {
                -webkit-user-select: none;
                user-select: none;
            }

            .content {
                -webkit-overflow-scrolling: touch;
            }

            #closeSidebarMobile {
                opacity: 0.8;
                cursor: pointer;
                z-index: 1060;
            }

            input, select, textarea, button {
                font-size: 16px !important;
            }

            .table-responsive {
                -webkit-overflow-scrolling: touch;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <?php include 'sidebar.php'; ?>

        <!-- Page Content -->
        <div class="content">
            <!-- Top Navbar -->
            <nav class="navbar navbar-expand-lg navbar-dark">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-primary">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="d-flex align-items-center">
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i> <?php echo $shopOwnerData['full_name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                                <li><a class="dropdown-item" href="change_password.php"><i class="fas fa-key me-2"></i> Change Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="bg-light py-2 px-3 mb-3">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                    <?php if (isset($breadcrumbs)): ?>
                        <?php
                        $isArrayOfArrays = isset($breadcrumbs[0]) && is_array($breadcrumbs[0]) && isset($breadcrumbs[0]['text']);

                        if ($isArrayOfArrays):
                            foreach ($breadcrumbs as $crumb):
                                $label = $crumb['text'];
                                $url = $crumb['link'];
                        ?>
                            <?php if ($url): ?>
                                <li class="breadcrumb-item"><a href="<?php echo $url; ?>"><?php echo $label; ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $label; ?></li>
                            <?php endif; ?>
                        <?php
                            endforeach;
                        else:
                            foreach ($breadcrumbs as $label => $url):
                        ?>
                            <?php if ($url): ?>
                                <li class="breadcrumb-item"><a href="<?php echo $url; ?>"><?php echo $label; ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $label; ?></li>
                            <?php endif; ?>
                        <?php
                            endforeach;
                        endif;
                        ?>
                    <?php endif; ?>
                </ol>
            </nav>

            <!-- Main Content Container -->
            <div class="container-fluid pb-4">
                <!-- Success Alert -->
                <?php if (isset($_SESSION['shop_success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $_SESSION['shop_success_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['shop_success_message']); ?>
                <?php endif; ?>

                <!-- Error Alert -->
                <?php if (isset($_SESSION['shop_error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $_SESSION['shop_error_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['shop_error_message']); ?>
                <?php endif; ?>

                <!-- Warning Alert -->
                <?php if (isset($_SESSION['shop_warning_message'])): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $_SESSION['shop_warning_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['shop_warning_message']); ?>
                <?php endif; ?>

                <!-- Info Alert -->
                <?php if (isset($_SESSION['shop_info_message'])): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $_SESSION['shop_info_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['shop_info_message']); ?>
                <?php endif; ?>
