<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/margin_extra_small"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/elevation_card_small">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_small">

        <ImageView
            android:id="@+id/item_image"
            android:layout_width="@dimen/icon_size_medium"
            android:layout_height="@dimen/icon_size_medium"
            android:contentDescription="@string/item_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/ic_laundry_placeholder" />

        <TextView
            android:id="@+id/item_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/item_image"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/item_subtotal"
            tools:text="Shirt Washing" />

        <TextView
            android:id="@+id/item_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_extra_small"
            android:textSize="@dimen/text_size_small"
            android:textColor="@color/text_secondary"
            app:layout_constraintStart_toEndOf="@id/item_image"
            app:layout_constraintTop_toBottomOf="@id/item_name"
            app:layout_constraintEnd_toStartOf="@id/item_subtotal"
            tools:text="৳50/piece" />

        <TextView
            android:id="@+id/item_quantity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_extra_small"
            android:textSize="@dimen/text_size_small"
            android:textColor="@color/text_secondary"
            app:layout_constraintStart_toEndOf="@id/item_image"
            app:layout_constraintTop_toBottomOf="@id/item_price"
            app:layout_constraintEnd_toStartOf="@id/item_subtotal"
            tools:text="Qty: 2 pieces" />

        <TextView
            android:id="@+id/item_subtotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="৳100" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
