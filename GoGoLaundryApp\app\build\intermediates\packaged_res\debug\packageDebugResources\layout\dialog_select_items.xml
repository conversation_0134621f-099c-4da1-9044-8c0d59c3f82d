<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_large">

        <ImageView
            android:id="@+id/dialog_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_washing_machine"
            android:tint="@color/primary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            android:text="Select Items"
            android:textSize="20sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/dialog_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/dialog_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            android:text="Please select specific items from this service to add them to your cart."
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/dialog_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
