<?php
/**
 * Payment Withdrawal Manager
 * 
 * Handles all payment withdrawal request operations for shop owners
 * including request creation, status updates, and admin management
 */

class PaymentWithdrawalManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Create a new withdrawal request
     */
    public function createWithdrawalRequest($shopId, $shopOwnerId, $requestData) {
        try {
            $this->pdo->beginTransaction();
            
            // Validate shop owner and get available balance
            $balance = $this->getShopOwnerBalance($shopId, $shopOwnerId);
            if (!$balance) {
                throw new Exception("Shop owner not found or no earnings available");
            }
            
            // Validate request amount
            if ($requestData['amount'] <= 0) {
                throw new Exception("Invalid withdrawal amount");
            }
            
            if ($requestData['amount'] > $balance['available_balance']) {
                throw new Exception("Insufficient balance. Available: ৳" . number_format($balance['available_balance'], 2));
            }
            
            // Validate payment method
            $paymentMethod = $this->getPaymentMethodSettings($requestData['payment_method']);
            if (!$paymentMethod || !$paymentMethod['is_active']) {
                throw new Exception("Invalid or inactive payment method");
            }
            
            // Check amount limits
            if ($requestData['amount'] < $paymentMethod['min_amount']) {
                throw new Exception("Minimum withdrawal amount is ৳" . number_format($paymentMethod['min_amount'], 2));
            }
            
            if ($requestData['amount'] > $paymentMethod['max_amount']) {
                throw new Exception("Maximum withdrawal amount is ৳" . number_format($paymentMethod['max_amount'], 2));
            }
            
            // Validate account number format
            if ($paymentMethod['account_number_pattern'] && 
                !preg_match('/' . $paymentMethod['account_number_pattern'] . '/', $requestData['account_number'])) {
                throw new Exception("Invalid account number format for " . $paymentMethod['display_name']);
            }
            
            // Calculate processing fee
            $processingFee = $this->calculateProcessingFee($requestData['amount'], $paymentMethod);
            $finalAmount = $requestData['amount'] - $processingFee;
            
            // Create withdrawal request
            $stmt = $this->pdo->prepare("
                INSERT INTO payment_withdrawal_requests (
                    shop_id, shop_owner_id, request_amount, available_balance, 
                    payment_method, account_type, account_number, account_holder_name,
                    shop_owner_notes, processing_fee, final_amount, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            $stmt->execute([
                $shopId,
                $shopOwnerId,
                $requestData['amount'],
                $balance['available_balance'],
                $requestData['payment_method'],
                $requestData['account_type'],
                $requestData['account_number'],
                $requestData['account_holder_name'],
                $requestData['notes'] ?? null,
                $processingFee,
                $finalAmount
            ]);
            
            $requestId = $this->pdo->lastInsertId();
            
            // Update shop owner earnings (reduce available balance, increase pending)
            $this->updateShopOwnerBalance($shopId, $shopOwnerId, [
                'pending_withdrawal' => $balance['pending_withdrawal'] + $requestData['amount'],
                'available_balance' => $balance['available_balance'] - $requestData['amount']
            ]);
            
            // Log status history
            $this->logStatusChange($requestId, null, 'pending', null, 'Withdrawal request created by shop owner');
            
            $this->pdo->commit();
            return $requestId;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw $e;
        }
    }
    
    /**
     * Update withdrawal request status (Admin function)
     */
    public function updateRequestStatus($requestId, $newStatus, $adminId, $data = []) {
        try {
            $this->pdo->beginTransaction();
            
            // Get current request
            $request = $this->getWithdrawalRequest($requestId);
            if (!$request) {
                throw new Exception("Withdrawal request not found");
            }
            
            $oldStatus = $request['status'];
            
            // Validate status transition
            if (!$this->isValidStatusTransition($oldStatus, $newStatus)) {
                throw new Exception("Invalid status transition from {$oldStatus} to {$newStatus}");
            }
            
            // Prepare update data
            $updateData = [
                'status' => $newStatus,
                'admin_id' => $adminId,
                'admin_notes' => $data['admin_notes'] ?? null
            ];
            
            // Handle specific status updates
            switch ($newStatus) {
                case 'processing':
                    $updateData['processed_at'] = date('Y-m-d H:i:s');
                    break;
                    
                case 'approved':
                case 'completed':
                    $updateData['transaction_reference'] = $data['transaction_reference'] ?? null;
                    $updateData['completed_at'] = date('Y-m-d H:i:s');
                    
                    // Update shop owner earnings
                    $this->updateShopOwnerBalance($request['shop_id'], $request['shop_owner_id'], [
                        'withdrawn_amount' => $request['withdrawn_amount'] + $request['request_amount'],
                        'pending_withdrawal' => $request['pending_withdrawal'] - $request['request_amount'],
                        'last_withdrawal_date' => date('Y-m-d H:i:s')
                    ]);
                    break;
                    
                case 'rejected':
                case 'cancelled':
                    $updateData['rejection_reason'] = $data['rejection_reason'] ?? null;
                    
                    // Restore available balance
                    $this->updateShopOwnerBalance($request['shop_id'], $request['shop_owner_id'], [
                        'available_balance' => $request['available_balance'] + $request['request_amount'],
                        'pending_withdrawal' => $request['pending_withdrawal'] - $request['request_amount']
                    ]);
                    break;
            }
            
            // Update request
            $setClause = implode(', ', array_map(fn($key) => "$key = ?", array_keys($updateData)));
            $stmt = $this->pdo->prepare("UPDATE payment_withdrawal_requests SET $setClause WHERE id = ?");
            $stmt->execute([...array_values($updateData), $requestId]);
            
            // Log status change
            $this->logStatusChange($requestId, $oldStatus, $newStatus, $adminId, $data['change_reason'] ?? null, $data['admin_notes'] ?? null);
            
            $this->pdo->commit();
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get withdrawal request by ID
     */
    public function getWithdrawalRequest($requestId) {
        $stmt = $this->pdo->prepare("
            SELECT pwr.*, ls.name as shop_name, ls.owner_name, so.username as shop_owner_username,
                   soe.withdrawn_amount, soe.pending_withdrawal, soe.available_balance
            FROM payment_withdrawal_requests pwr
            JOIN laundry_shops ls ON pwr.shop_id = ls.id
            JOIN shop_owners so ON pwr.shop_owner_id = so.id
            LEFT JOIN shop_owner_earnings soe ON pwr.shop_id = soe.shop_id AND pwr.shop_owner_id = soe.shop_owner_id
            WHERE pwr.id = ?
        ");
        $stmt->execute([$requestId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get withdrawal requests with filters
     */
    public function getWithdrawalRequests($filters = [], $limit = 20, $offset = 0) {
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($filters['status'])) {
            $whereClause .= " AND pwr.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['shop_id'])) {
            $whereClause .= " AND pwr.shop_id = ?";
            $params[] = $filters['shop_id'];
        }
        
        if (!empty($filters['payment_method'])) {
            $whereClause .= " AND pwr.payment_method = ?";
            $params[] = $filters['payment_method'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereClause .= " AND DATE(pwr.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause .= " AND DATE(pwr.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $sql = "
            SELECT pwr.*, ls.name as shop_name, ls.owner_name, so.username as shop_owner_username,
                   pms.display_name as payment_method_name, a.username as admin_username
            FROM payment_withdrawal_requests pwr
            JOIN laundry_shops ls ON pwr.shop_id = ls.id
            JOIN shop_owners so ON pwr.shop_owner_id = so.id
            LEFT JOIN payment_method_settings pms ON pwr.payment_method = pms.method_name
            LEFT JOIN admins a ON pwr.admin_id = a.id
            $whereClause
            ORDER BY pwr.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get withdrawal requests count
     */
    public function getWithdrawalRequestsCount($filters = []) {
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($filters['status'])) {
            $whereClause .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['shop_id'])) {
            $whereClause .= " AND shop_id = ?";
            $params[] = $filters['shop_id'];
        }
        
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM payment_withdrawal_requests $whereClause");
        $stmt->execute($params);
        return $stmt->fetchColumn();
    }
    
    /**
     * Get shop owner balance
     */
    public function getShopOwnerBalance($shopId, $shopOwnerId) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM shop_owner_earnings 
            WHERE shop_id = ? AND shop_owner_id = ?
        ");
        $stmt->execute([$shopId, $shopOwnerId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Update shop owner balance
     */
    private function updateShopOwnerBalance($shopId, $shopOwnerId, $updates) {
        $setClause = implode(', ', array_map(fn($key) => "$key = ?", array_keys($updates)));
        $stmt = $this->pdo->prepare("
            UPDATE shop_owner_earnings 
            SET $setClause 
            WHERE shop_id = ? AND shop_owner_id = ?
        ");
        $stmt->execute([...array_values($updates), $shopId, $shopOwnerId]);
    }
    
    /**
     * Get payment method settings
     */
    public function getPaymentMethodSettings($methodName = null) {
        if ($methodName) {
            $stmt = $this->pdo->prepare("SELECT * FROM payment_method_settings WHERE method_name = ?");
            $stmt->execute([$methodName]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $stmt = $this->pdo->query("SELECT * FROM payment_method_settings WHERE is_active = 1 ORDER BY display_name");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
    
    /**
     * Calculate processing fee
     */
    private function calculateProcessingFee($amount, $paymentMethod) {
        if ($paymentMethod['processing_fee_type'] === 'percentage') {
            return ($amount * $paymentMethod['processing_fee_value']) / 100;
        } else {
            return $paymentMethod['processing_fee_value'];
        }
    }
    
    /**
     * Log status change
     */
    private function logStatusChange($requestId, $oldStatus, $newStatus, $changedBy, $reason = null, $adminNotes = null) {
        $stmt = $this->pdo->prepare("
            INSERT INTO payment_withdrawal_status_history 
            (withdrawal_request_id, old_status, new_status, changed_by, change_reason, admin_notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$requestId, $oldStatus, $newStatus, $changedBy, $reason, $adminNotes]);
    }
    
    /**
     * Validate status transition
     */
    private function isValidStatusTransition($oldStatus, $newStatus) {
        $validTransitions = [
            'pending' => ['processing', 'rejected', 'cancelled'],
            'processing' => ['approved', 'completed', 'rejected', 'cancelled'],
            'approved' => ['completed', 'cancelled'],
            'completed' => [], // Final state
            'rejected' => [], // Final state
            'cancelled' => [] // Final state
        ];
        
        return in_array($newStatus, $validTransitions[$oldStatus] ?? []);
    }
    
    /**
     * Get withdrawal statistics
     */
    public function getWithdrawalStatistics($shopId = null) {
        $whereClause = $shopId ? "WHERE shop_id = ?" : "";
        $params = $shopId ? [$shopId] : [];
        
        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
                COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_requests,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
                COALESCE(SUM(CASE WHEN status = 'completed' THEN request_amount ELSE 0 END), 0) as total_withdrawn,
                COALESCE(SUM(CASE WHEN status IN ('pending', 'processing') THEN request_amount ELSE 0 END), 0) as pending_amount
            FROM payment_withdrawal_requests
            $whereClause
        ");
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
