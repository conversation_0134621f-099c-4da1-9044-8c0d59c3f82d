<?php
/**
 * Shop Owner Withdrawal Request Page
 *
 * Allows shop owners to submit withdrawal requests for their earnings
 */

// Include shop owner authentication
require_once 'auth.php';
require_once '../includes/PaymentWithdrawalManager.php';

$pageTitle = 'Withdrawal Requests';
$currentPage = 'withdrawal';

// Initialize managers
$paymentWithdrawalManager = new PaymentWithdrawalManager($pdo);

// Get shop owner's balance
$balance = $paymentWithdrawalManager->getShopOwnerBalance($shopOwnerData['shop_id'], $shopOwnerData['id']);
if (!$balance) {
    // Initialize balance if not exists
    $stmt = $pdo->prepare("
        INSERT INTO shop_owner_earnings (shop_id, shop_owner_id, total_earnings, available_balance)
        VALUES (?, ?, 0, 0)
        ON DUPLICATE KEY UPDATE shop_id = shop_id
    ");
    $stmt->execute([$shopOwnerData['shop_id'], $shopOwnerData['id']]);

    $balance = [
        'total_earnings' => 0,
        'withdrawn_amount' => 0,
        'pending_withdrawal' => 0,
        'available_balance' => 0
    ];
}

// Get payment methods
$paymentMethods = $paymentWithdrawalManager->getPaymentMethodSettings();

// Handle withdrawal request submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_withdrawal') {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['shop_csrf_token']) {
            throw new Exception("Invalid security token. Please try again.");
        }

        $requestData = [
            'amount' => floatval($_POST['amount']),
            'payment_method' => sanitize($_POST['payment_method']),
            'account_type' => sanitize($_POST['account_type']),
            'account_number' => sanitize($_POST['account_number']),
            'account_holder_name' => sanitize($_POST['account_holder_name']),
            'notes' => sanitize($_POST['notes'] ?? '')
        ];

        $requestId = $paymentWithdrawalManager->createWithdrawalRequest(
            $shopOwnerData['shop_id'],
            $shopOwnerData['id'],
            $requestData
        );

        $success = "Withdrawal request submitted successfully! Request ID: #$requestId";

        // Refresh balance
        $balance = $paymentWithdrawalManager->getShopOwnerBalance($shopOwnerData['shop_id'], $shopOwnerData['id']);

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get recent withdrawal requests
$recentRequests = $paymentWithdrawalManager->getWithdrawalRequests(
    ['shop_id' => $shopOwnerData['shop_id']],
    10,
    0
);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-money-bill-wave me-2"></i>Withdrawal Requests
                </h1>
                <p class="page-subtitle">Manage your earnings and submit withdrawal requests</p>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Balance Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Earnings</h6>
                            <h4 class="mb-0">৳<?php echo number_format($balance['total_earnings'], 2); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Available Balance</h6>
                            <h4 class="mb-0">৳<?php echo number_format($balance['available_balance'], 2); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Pending Withdrawal</h6>
                            <h4 class="mb-0">৳<?php echo number_format($balance['pending_withdrawal'], 2); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Withdrawn</h6>
                            <h4 class="mb-0">৳<?php echo number_format($balance['withdrawn_amount'], 2); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Withdrawal Request Form -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>New Withdrawal Request
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($balance['available_balance'] <= 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            You don't have sufficient balance to make a withdrawal request.
                        </div>
                    <?php else: ?>
                        <form method="POST" id="withdrawalForm">
                            <input type="hidden" name="action" value="submit_withdrawal">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['shop_csrf_token']; ?>">

                            <div class="mb-3">
                                <label for="amount" class="form-label">Withdrawal Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    <input type="number" class="form-control" id="amount" name="amount"
                                           min="1" max="<?php echo $balance['available_balance']; ?>"
                                           step="0.01" required>
                                </div>
                                <div class="form-text">
                                    Available balance: ৳<?php echo number_format($balance['available_balance'], 2); ?>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">Select Payment Method</option>
                                    <?php foreach ($paymentMethods as $method): ?>
                                        <?php if ($method['is_active']): ?>
                                            <option value="<?php echo $method['method_name']; ?>"
                                                    data-min="<?php echo $method['min_amount']; ?>"
                                                    data-max="<?php echo $method['max_amount']; ?>"
                                                    data-fee-type="<?php echo $method['processing_fee_type']; ?>"
                                                    data-fee-value="<?php echo $method['processing_fee_value']; ?>"
                                                    data-pattern="<?php echo htmlspecialchars($method['account_number_pattern'] ?? ''); ?>"
                                                    data-example="<?php echo htmlspecialchars($method['account_number_example'] ?? ''); ?>"
                                                    data-instructions="<?php echo htmlspecialchars($method['instructions'] ?? ''); ?>">
                                                <?php echo $method['display_name']; ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                                <div id="method_info" class="form-text"></div>
                            </div>

                            <div class="mb-3">
                                <label for="account_type" class="form-label">Account Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">Select Account Type</option>
                                    <option value="personal">Personal Account</option>
                                    <option value="agent">Agent Account</option>
                                    <option value="merchant">Merchant Account</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="account_number" name="account_number" required>
                                <div id="account_example" class="form-text"></div>
                            </div>

                            <div class="mb-3">
                                <label for="account_holder_name" class="form-label">Account Holder Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="account_holder_name" name="account_holder_name"
                                       value="<?php echo htmlspecialchars($shopOwnerData['full_name']); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Additional Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="Any additional information or special instructions..."></textarea>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Withdrawal Request
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Requests -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Withdrawal Requests
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recentRequests)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No withdrawal requests found.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentRequests as $request): ?>
                                        <tr>
                                            <td>#<?= $request['id'] ?></td>
                                            <td>৳<?= number_format($request['request_amount'], 2) ?></td>
                                            <td><?= ucfirst($request['payment_method']) ?></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'processing' => 'info',
                                                    'approved' => 'success',
                                                    'completed' => 'success',
                                                    'rejected' => 'danger',
                                                    'cancelled' => 'secondary'
                                                ];
                                                $class = $statusClass[$request['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $class ?>">
                                                    <?= ucfirst($request['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($request['created_at'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('payment_method');
    const amountInput = document.getElementById('amount');
    const accountNumberInput = document.getElementById('account_number');

    // Update method info and validation when payment method changes
    paymentMethodSelect.addEventListener('change', function() {
        const option = this.selectedOptions[0];
        const methodInfo = document.getElementById('method_info');
        const accountExample = document.getElementById('account_example');

        if (option.value) {
            const minAmount = parseFloat(option.dataset.min);
            const maxAmount = parseFloat(option.dataset.max);
            const feeType = option.dataset.feeType;
            const feeValue = parseFloat(option.dataset.feeValue);
            const pattern = option.dataset.pattern;
            const example = option.dataset.example;
            const instructions = option.dataset.instructions;

            // Update amount input constraints
            amountInput.min = minAmount;
            amountInput.max = Math.min(maxAmount, <?php echo $balance['available_balance']; ?>);

            // Update method info
            let feeText = '';
            if (feeType === 'percentage') {
                feeText = `${feeValue}% processing fee`;
            } else {
                feeText = `৳${feeValue} processing fee`;
            }

            methodInfo.innerHTML = `
                <div class="text-info">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        Amount range: ৳${minAmount} - ৳${Math.min(maxAmount, <?php echo $balance['available_balance']; ?>)} | ${feeText}
                        <br>${instructions}
                    </small>
                </div>
            `;

            // Update account number example
            if (example) {
                accountExample.innerHTML = `<small class="text-muted">Example: ${example}</small>`;
            }

            // Set pattern for account number validation
            if (pattern) {
                accountNumberInput.pattern = pattern;
            } else {
                accountNumberInput.removeAttribute('pattern');
            }
        } else {
            methodInfo.innerHTML = '';
            accountExample.innerHTML = '';
            amountInput.min = 1;
            amountInput.max = <?php echo $balance['available_balance']; ?>;
            accountNumberInput.removeAttribute('pattern');
        }
    });

    // Calculate and display processing fee when amount changes
    amountInput.addEventListener('input', function() {
        const option = paymentMethodSelect.selectedOptions[0];
        if (option && option.value && this.value) {
            const amount = parseFloat(this.value);
            const feeType = option.dataset.feeType;
            const feeValue = parseFloat(option.dataset.feeValue);

            let processingFee = 0;
            if (feeType === 'percentage') {
                processingFee = (amount * feeValue) / 100;
            } else {
                processingFee = feeValue;
            }

            const finalAmount = amount - processingFee;

            const methodInfo = document.getElementById('method_info');
            const currentInfo = methodInfo.innerHTML;

            if (currentInfo.includes('Processing fee:')) {
                // Update existing fee info
                methodInfo.innerHTML = currentInfo.replace(
                    /Processing fee:.*?<br>/,
                    `Processing fee: ৳${processingFee.toFixed(2)} | You will receive: ৳${finalAmount.toFixed(2)}<br>`
                );
            } else {
                // Add fee info
                methodInfo.innerHTML = currentInfo + `
                    <br><small class="text-warning">
                        <i class="fas fa-calculator me-1"></i>
                        Processing fee: ৳${processingFee.toFixed(2)} | You will receive: ৳${finalAmount.toFixed(2)}
                    </small>
                `;
            }
        }
    });

    // Form validation
    document.getElementById('withdrawalForm').addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value);
        const paymentMethod = paymentMethodSelect.value;
        const accountNumber = accountNumberInput.value;

        if (!paymentMethod) {
            e.preventDefault();
            alert('Please select a payment method.');
            return;
        }

        const option = paymentMethodSelect.selectedOptions[0];
        const minAmount = parseFloat(option.dataset.min);
        const maxAmount = parseFloat(option.dataset.max);

        if (amount < minAmount || amount > maxAmount) {
            e.preventDefault();
            alert(`Amount must be between ৳${minAmount} and ৳${maxAmount} for ${option.text}.`);
            return;
        }

        if (amount > <?php echo $balance['available_balance']; ?>) {
            e.preventDefault();
            alert('Amount exceeds your available balance.');
            return;
        }

        // Validate account number pattern if exists
        const pattern = option.dataset.pattern;
        if (pattern && !new RegExp(pattern).test(accountNumber)) {
            e.preventDefault();
            alert('Please enter a valid account number for the selected payment method.');
            return;
        }

        // Confirm submission
        if (!confirm(`Are you sure you want to submit a withdrawal request for ৳${amount.toFixed(2)}?`)) {
            e.preventDefault();
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
