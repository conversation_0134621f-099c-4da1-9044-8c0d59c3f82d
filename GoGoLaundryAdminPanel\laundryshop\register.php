<?php
/**
 * Shop Owner Registration Page
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ShopOwnerManager.php';

// Redirect if already logged in
if (isset($_SESSION['shop_owner_logged_in']) && $_SESSION['shop_owner_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $shopName = sanitize($_POST['shop_name'] ?? '');
    $ownerName = sanitize($_POST['owner_name'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $address = sanitize($_POST['address'] ?? '');
    $latitude = floatval($_POST['latitude'] ?? 0);
    $longitude = floatval($_POST['longitude'] ?? 0);
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';

    // Validation
    if (empty($shopName) || empty($ownerName) || empty($phone) || empty($address) || 
        empty($username) || empty($password) || empty($confirmPassword)) {
        $error = 'Please fill in all required fields.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } elseif ($latitude == 0 || $longitude == 0) {
        $error = 'Please select a valid location on the map.';
    } else {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Create shop first
            $shopStmt = $pdo->prepare("
                INSERT INTO laundry_shops (name, owner_name, phone, email, address, latitude, longitude, 
                                         commission_percentage, is_active, is_verified)
                VALUES (?, ?, ?, ?, ?, ?, ?, 15.00, 1, 0)
            ");

            if ($shopStmt->execute([$shopName, $ownerName, $phone, $email, $address, $latitude, $longitude])) {
                $shopId = $pdo->lastInsertId();

                // Create shop owner account
                $shopOwnerManager = new ShopOwnerManager($pdo);
                $shopOwnerId = $shopOwnerManager->createShopOwner($shopId, $username, $password, $email, $phone, $ownerName);

                if ($shopOwnerId) {
                    $pdo->commit();
                    $success = 'Registration successful! Your shop has been submitted for admin approval. You will be notified once approved.';
                } else {
                    $pdo->rollBack();
                    $error = 'Failed to create shop owner account. Username may already exist.';
                }
            } else {
                $pdo->rollBack();
                $error = 'Failed to register shop. Phone number may already exist.';
            }
        } catch (PDOException $e) {
            $pdo->rollBack();
            error_log('Shop registration error: ' . $e->getMessage());
            if (strpos($e->getMessage(), 'phone') !== false) {
                $error = 'Phone number already registered.';
            } elseif (strpos($e->getMessage(), 'email') !== false) {
                $error = 'Email already registered.';
            } else {
                $error = 'Registration failed. Please try again.';
            }
        }
    }
}

$pageTitle = 'Shop Registration';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .register-header h2 {
            margin: 0;
            font-weight: 300;
        }
        
        .register-body {
            padding: 2rem;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 500;
        }
        
        #map {
            height: 300px;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .location-buttons {
            margin-bottom: 1rem;
        }
        
        .location-buttons .btn {
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="register-header">
                <i class="fas fa-store fa-3x mb-3"></i>
                <h2>Register Your Laundry Shop</h2>
                <div class="subtitle">Join <?php echo APP_NAME; ?> Network</div>
            </div>
            
            <div class="register-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success); ?>
                        <div class="mt-2">
                            <a href="login.php" class="btn btn-success btn-sm">Go to Login</a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (!$success): ?>
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shop_name" class="form-label">Shop Name *</label>
                                <input type="text" class="form-control" id="shop_name" name="shop_name" 
                                       value="<?php echo htmlspecialchars($_POST['shop_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="owner_name" class="form-label">Owner Name *</label>
                                <input type="text" class="form-control" id="owner_name" name="owner_name" 
                                       value="<?php echo htmlspecialchars($_POST['owner_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Address *</label>
                        <textarea class="form-control" id="address" name="address" rows="2" required><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <!-- Location Section -->
                    <div class="mb-3">
                        <label class="form-label">Shop Location *</label>
                        <div class="location-buttons">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="goToLocation(23.8103, 90.4125, 'Dhaka')">Dhaka</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="goToLocation(22.3569, 91.7832, 'Chittagong')">Chittagong</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="goToLocation(24.3636, 88.6241, 'Rajshahi')">Rajshahi</button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="getCurrentLocation()">
                                <i class="fas fa-location-arrow"></i> My Location
                            </button>
                        </div>
                        <div id="map"></div>
                        <small class="text-muted">Click on the map to set your shop location</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude *</label>
                                <input type="number" class="form-control" id="latitude" name="latitude" 
                                       step="any" value="<?php echo htmlspecialchars($_POST['latitude'] ?? ''); ?>" required readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude *</label>
                                <input type="number" class="form-control" id="longitude" name="longitude" 
                                       step="any" value="<?php echo htmlspecialchars($_POST['longitude'] ?? ''); ?>" required readonly>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                <small class="text-muted">This will be used to log in to your shop panel</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <small class="text-muted">Minimum 8 characters</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-register">
                            <i class="fas fa-user-plus me-2"></i>Register Shop
                        </button>
                    </div>
                </form>
                <?php endif; ?>
                
                <div class="text-center mt-3">
                    <a href="login.php" class="text-decoration-none">
                        <i class="fas fa-sign-in-alt me-1"></i>Already have an account? Login
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        let map;
        let marker;
        
        // Initialize map
        document.addEventListener('DOMContentLoaded', function() {
            initializeMap();
        });
        
        function initializeMap() {
            // Default location (Dhaka, Bangladesh)
            map = L.map('map').setView([23.8103, 90.4125], 13);
            
            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Add click listener to map
            map.on('click', function(e) {
                setMapLocation(e.latlng.lat, e.latlng.lng);
            });
        }
        
        function setMapLocation(lat, lng) {
            // Remove existing marker
            if (marker) {
                map.removeLayer(marker);
            }
            
            // Add new marker
            marker = L.marker([lat, lng]).addTo(map);
            
            // Update coordinate inputs
            document.getElementById('latitude').value = lat.toFixed(8);
            document.getElementById('longitude').value = lng.toFixed(8);
            
            // Center map on location
            map.setView([lat, lng], map.getZoom());
        }
        
        function goToLocation(lat, lng, name) {
            if (map) {
                map.setView([lat, lng], 13);
                setMapLocation(lat, lng);
            }
        }
        
        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    if (map) {
                        map.setView([lat, lng], 15);
                        setMapLocation(lat, lng);
                    }
                }, function(error) {
                    alert('Unable to get your location. Please select manually on the map.');
                });
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }
    </script>
</body>
</html>
