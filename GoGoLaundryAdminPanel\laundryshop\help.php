<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Help & Support';
$currentPage = 'help';

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Help & Support</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<!-- Quick Help Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Phone Support</h5>
                <p class="card-text">Call our support team for immediate assistance</p>
                <a href="tel:+8801234567890" class="btn btn-primary">
                    <i class="fas fa-phone me-2"></i>Call Now
                </a>
                <div class="mt-2">
                    <small class="text-muted">+880 ************</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fas fa-envelope fa-3x text-success mb-3"></i>
                <h5 class="card-title">Email Support</h5>
                <p class="card-text">Send us an email and we'll respond within 24 hours</p>
                <a href="mailto:<EMAIL>" class="btn btn-success">
                    <i class="fas fa-envelope me-2"></i>Send Email
                </a>
                <div class="mt-2">
                    <small class="text-muted"><EMAIL></small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="fab fa-whatsapp fa-3x text-success mb-3"></i>
                <h5 class="card-title">WhatsApp Support</h5>
                <p class="card-text">Chat with us on WhatsApp for quick help</p>
                <a href="https://wa.me/8801234567890" target="_blank" class="btn btn-success">
                    <i class="fab fa-whatsapp me-2"></i>Chat Now
                </a>
                <div class="mt-2">
                    <small class="text-muted">+880 ************</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-question-circle me-2"></i>Frequently Asked Questions
        </h5>
    </div>
    <div class="card-body">
        <div class="accordion" id="faqAccordion">
            <!-- FAQ 1 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="faq1">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#collapse1" aria-expanded="false" aria-controls="collapse1">
                        How do I manage my shop's services and items?
                    </button>
                </h2>
                <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="faq1" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        <p>You can manage your shop's services and items through the following pages:</p>
                        <ul>
                            <li><strong>Services:</strong> Go to <a href="services.php">My Services</a> to enable/disable services for your shop</li>
                            <li><strong>Items:</strong> Visit <a href="items.php">My Items</a> to manage laundry items and set custom pricing</li>
                            <li><strong>Pricing:</strong> Use <a href="pricing.php">Pricing Management</a> for bulk price updates and detailed pricing control</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- FAQ 2 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="faq2">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                        How do I update order status?
                    </button>
                </h2>
                <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="faq2" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        <p>To update order status:</p>
                        <ol>
                            <li>Go to <a href="orders.php">Orders Management</a></li>
                            <li>Find the order you want to update</li>
                            <li>Click on the "Actions" dropdown for that order</li>
                            <li>Select "Update Status"</li>
                            <li>Choose the new status and save</li>
                        </ol>
                        <p><strong>Order Status Flow:</strong> New Order → Processing → Out for Delivery → Completed</p>
                    </div>
                </div>
            </div>
            
            <!-- FAQ 3 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="faq3">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                        How do I update my shop information?
                    </button>
                </h2>
                <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="faq3" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        <p>You can update different aspects of your shop information:</p>
                        <ul>
                            <li><strong>Basic Info:</strong> Visit <a href="profile.php">Shop Profile</a> to update name, description, contact details</li>
                            <li><strong>Location & Hours:</strong> Go to <a href="shop_settings.php">Shop Settings</a> to update location and operating hours</li>
                            <li><strong>Account Info:</strong> Use <a href="account.php">My Account</a> to update your personal information</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- FAQ 4 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="faq4">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                        How do I view my sales reports?
                    </button>
                </h2>
                <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="faq4" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        <p>Access detailed reports through the Reports section:</p>
                        <ul>
                            <li><strong>Sales Report:</strong> <a href="sales_report.php">Sales Report</a> shows revenue, orders, and trends over time</li>
                            <li><strong>Order Analytics:</strong> <a href="order_report.php">Order Analytics</a> provides insights into order patterns and customer behavior</li>
                        </ul>
                        <p>You can filter reports by date range and export them for your records.</p>
                    </div>
                </div>
            </div>
            
            <!-- FAQ 5 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="faq5">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#collapse5" aria-expanded="false" aria-controls="collapse5">
                        What is the commission structure?
                    </button>
                </h2>
                <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="faq5" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        <p>The commission structure works as follows:</p>
                        <ul>
                            <li>A percentage of each completed order is charged as commission</li>
                            <li>Commission rates are set by the admin and may vary by shop</li>
                            <li>You can view your commission rate in <a href="profile.php">Shop Profile</a></li>
                            <li>Commission is automatically calculated on completed orders</li>
                            <li>Detailed commission information is available in your <a href="sales_report.php">Sales Reports</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- FAQ 6 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="faq6">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                            data-bs-target="#collapse6" aria-expanded="false" aria-controls="collapse6">
                        How do I change my password?
                    </button>
                </h2>
                <div id="collapse6" class="accordion-collapse collapse" aria-labelledby="faq6" data-bs-parent="#faqAccordion">
                    <div class="accordion-body">
                        <p>To change your password:</p>
                        <ol>
                            <li>Go to <a href="change_password.php">Change Password</a></li>
                            <li>Enter your current password</li>
                            <li>Enter your new password (minimum 8 characters)</li>
                            <li>Confirm your new password</li>
                            <li>Click "Change Password"</li>
                        </ol>
                        <p><strong>Security Tips:</strong> Use a strong password with uppercase, lowercase, numbers, and special characters.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Guide -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-book me-2"></i>Quick Start Guide
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-play-circle text-primary me-2"></i>Getting Started</h6>
                <ol>
                    <li>Complete your <a href="profile.php">shop profile</a></li>
                    <li>Set up your <a href="services.php">services</a> and <a href="items.php">items</a></li>
                    <li>Configure your <a href="pricing.php">pricing</a></li>
                    <li>Update your <a href="shop_settings.php">shop settings</a></li>
                    <li>Start receiving and managing <a href="orders.php">orders</a></li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-chart-line text-success me-2"></i>Growing Your Business</h6>
                <ul>
                    <li>Monitor your <a href="sales_report.php">sales performance</a></li>
                    <li>Analyze <a href="order_report.php">customer patterns</a></li>
                    <li>Optimize your pricing strategy</li>
                    <li>Maintain high service quality</li>
                    <li>Respond quickly to customer orders</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-address-card me-2"></i>Contact Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Support Hours</h6>
                <p class="text-muted">
                    Monday - Friday: 9:00 AM - 6:00 PM<br>
                    Saturday: 10:00 AM - 4:00 PM<br>
                    Sunday: Closed
                </p>
                
                <h6>Emergency Support</h6>
                <p class="text-muted">
                    For urgent technical issues, call our 24/7 emergency line:<br>
                    <strong>+880 ************</strong>
                </p>
            </div>
            <div class="col-md-6">
                <h6>Office Address</h6>
                <p class="text-muted">
                    GoGoLaundry Support Center<br>
                    123 Business District<br>
                    Dhaka 1000, Bangladesh
                </p>
                
                <h6>Response Times</h6>
                <p class="text-muted">
                    Phone: Immediate<br>
                    WhatsApp: Within 1 hour<br>
                    Email: Within 24 hours
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.accordion-button:not(.collapsed) {
    background-color: var(--shop-primary);
    color: white;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}
</style>

<?php include 'includes/footer.php'; ?>
