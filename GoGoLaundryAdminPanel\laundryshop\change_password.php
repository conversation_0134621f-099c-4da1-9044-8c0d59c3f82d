<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Change Password';
$currentPage = 'change_password';

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_password') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $_SESSION['shop_error_message'] = 'Please fill in all password fields.';
    } elseif ($newPassword !== $confirmPassword) {
        $_SESSION['shop_error_message'] = 'New passwords do not match.';
    } elseif (strlen($newPassword) < 8) {
        $_SESSION['shop_error_message'] = 'New password must be at least 8 characters long.';
    } else {
        try {
            // Get current password hash
            $currentStmt = $pdo->prepare("SELECT password FROM shop_owners WHERE id = ?");
            $currentStmt->execute([$shopOwnerData['id']]);
            $currentHash = $currentStmt->fetchColumn();
            
            // Verify current password
            if (!password_verify($currentPassword, $currentHash)) {
                $_SESSION['shop_error_message'] = 'Current password is incorrect.';
            } else {
                // Update password
                if ($shopOwnerManager->updatePassword($shopOwnerData['id'], $newPassword)) {
                    $_SESSION['shop_success_message'] = 'Password changed successfully!';
                    
                    // Clear form data
                    $_POST = [];
                } else {
                    $_SESSION['shop_error_message'] = 'Failed to change password. Please try again.';
                }
            }
        } catch (PDOException $e) {
            error_log('Password change error: ' . $e->getMessage());
            $_SESSION['shop_error_message'] = 'Failed to change password. Please try again.';
        }
    }
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Change Password</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="account.php" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>Back to Account
        </a>
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>Change Your Password
                </h5>
                <small class="text-muted">Keep your account secure with a strong password</small>
            </div>
            <div class="card-body">
                <form method="POST" id="changePasswordForm">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                   minlength="8" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">Password must be at least 8 characters long</div>
                        
                        <!-- Password strength indicator -->
                        <div class="password-strength mt-2" id="password_strength" style="display: none;">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="strength_bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="strength_text" class="text-muted"></small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   minlength="8" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback" id="confirm_feedback">
                            Passwords do not match
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="submit_btn">
                            <i class="fas fa-save me-2"></i>Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Password Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Password Security Tips
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use at least 8 characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include uppercase and lowercase letters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include numbers and special characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Avoid using personal information
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        Don't reuse passwords from other accounts
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.password-strength .progress-bar {
    transition: width 0.3s ease;
}

.strength-weak { background-color: #dc3545; }
.strength-fair { background-color: #ffc107; }
.strength-good { background-color: #17a2b8; }
.strength-strong { background-color: #28a745; }
</style>

<script>
// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 8) strength += 1;
    if (password.match(/[a-z]/)) strength += 1;
    if (password.match(/[A-Z]/)) strength += 1;
    if (password.match(/[0-9]/)) strength += 1;
    if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
    
    const strengthBar = document.getElementById('strength_bar');
    const strengthText = document.getElementById('strength_text');
    
    switch (strength) {
        case 0:
        case 1:
            strengthBar.style.width = '20%';
            strengthBar.className = 'progress-bar strength-weak';
            feedback = 'Very Weak';
            break;
        case 2:
            strengthBar.style.width = '40%';
            strengthBar.className = 'progress-bar strength-weak';
            feedback = 'Weak';
            break;
        case 3:
            strengthBar.style.width = '60%';
            strengthBar.className = 'progress-bar strength-fair';
            feedback = 'Fair';
            break;
        case 4:
            strengthBar.style.width = '80%';
            strengthBar.className = 'progress-bar strength-good';
            feedback = 'Good';
            break;
        case 5:
            strengthBar.style.width = '100%';
            strengthBar.className = 'progress-bar strength-strong';
            feedback = 'Strong';
            break;
    }
    
    strengthText.textContent = feedback;
    return strength;
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const strengthIndicator = document.getElementById('password_strength');
    const submitBtn = document.getElementById('submit_btn');
    
    // Show strength indicator when typing new password
    newPasswordField.addEventListener('input', function() {
        const password = this.value;
        
        if (password.length > 0) {
            strengthIndicator.style.display = 'block';
            checkPasswordStrength(password);
        } else {
            strengthIndicator.style.display = 'none';
        }
        
        validatePasswords();
    });
    
    // Validate password confirmation
    confirmPasswordField.addEventListener('input', validatePasswords);
    
    function validatePasswords() {
        const newPassword = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword.length > 0) {
            if (newPassword === confirmPassword) {
                confirmPasswordField.classList.remove('is-invalid');
                confirmPasswordField.classList.add('is-valid');
                submitBtn.disabled = false;
            } else {
                confirmPasswordField.classList.remove('is-valid');
                confirmPasswordField.classList.add('is-invalid');
                submitBtn.disabled = true;
            }
        } else {
            confirmPasswordField.classList.remove('is-valid', 'is-invalid');
            submitBtn.disabled = false;
        }
    }
    
    // Form submission
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        const newPassword = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match!');
            return false;
        }
        
        if (newPassword.length < 8) {
            e.preventDefault();
            alert('Password must be at least 8 characters long!');
            return false;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing Password...';
    });
});
</script>

<?php include 'includes/footer.php'; ?>
