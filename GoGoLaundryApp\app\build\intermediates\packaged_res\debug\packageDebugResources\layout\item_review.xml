<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="180dp"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:background="@drawable/glass_section_background"
        android:padding="16dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/reviewer_image"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:scaleType="centerCrop"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            tools:src="@drawable/ic_person" />

        <TextView
            android:id="@+id/reviewer_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="@drawable/glass_section_background"
            android:padding="6dp"
            android:elevation="2dp"
            tools:text="Kamal Hasan" />

        <TextView
            android:id="@+id/review_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:textColor="#E0E0E0"
            android:textSize="12sp"
            android:maxLines="2"
            android:ellipsize="end"
            android:background="@drawable/glass_section_background"
            android:padding="6dp"
            android:elevation="1dp"
            tools:text="Awesome service!" />

        <RatingBar
            android:id="@+id/rating_bar"
            style="?android:attr/ratingBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:isIndicator="true"
            android:numStars="5"
            android:stepSize="0.5"
            tools:rating="4.5" />

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
