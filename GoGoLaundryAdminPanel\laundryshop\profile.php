<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Shop Profile';
$currentPage = 'profile';

// Get shop details
try {
    $shopStmt = $pdo->prepare("
        SELECT ls.*, 
               d.name as division_name,
               dist.name as district_name,
               up.name as upazilla_name
        FROM laundry_shops ls
        LEFT JOIN divisions d ON ls.division_id = d.id
        LEFT JOIN districts dist ON ls.district_id = dist.id
        LEFT JOIN upazillas up ON ls.upazilla_id = up.id
        WHERE ls.id = ?
    ");
    $shopStmt->execute([$shopOwnerData['shop_id']]);
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Shop profile fetch error: ' . $e->getMessage());
    $shop = null;
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_profile') {
    $name = sanitize($_POST['name']);
    $bnName = sanitize($_POST['bn_name']);
    $description = sanitize($_POST['description']);
    $bnDescription = sanitize($_POST['bn_description']);
    $ownerName = sanitize($_POST['owner_name']);
    $phone = sanitize($_POST['phone']);
    $email = sanitize($_POST['email']);
    $address = sanitize($_POST['address']);
    
    try {
        $updateStmt = $pdo->prepare("
            UPDATE laundry_shops 
            SET name = ?, bn_name = ?, description = ?, bn_description = ?, 
                owner_name = ?, phone = ?, email = ?, address = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        if ($updateStmt->execute([$name, $bnName, $description, $bnDescription, $ownerName, $phone, $email, $address, $shopOwnerData['shop_id']])) {
            $_SESSION['shop_success_message'] = 'Shop profile updated successfully!';
            // Refresh shop data
            $shopStmt->execute([$shopOwnerData['shop_id']]);
            $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $_SESSION['shop_error_message'] = 'Failed to update shop profile.';
        }
    } catch (PDOException $e) {
        error_log('Shop profile update error: ' . $e->getMessage());
        $_SESSION['shop_error_message'] = 'Failed to update shop profile.';
    }
}

if (!$shop) {
    $_SESSION['shop_error_message'] = 'Shop not found.';
    header('Location: index.php');
    exit;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Shop Profile</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shop['name']); ?>
        </span>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Edit Shop Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Shop Name (English) *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($shop['name']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bn_name" class="form-label">Shop Name (Bengali)</label>
                                <input type="text" class="form-control" id="bn_name" name="bn_name" 
                                       value="<?php echo htmlspecialchars($shop['bn_name'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description (English)</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($shop['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bn_description" class="form-label">Description (Bengali)</label>
                        <textarea class="form-control" id="bn_description" name="bn_description" rows="3"><?php echo htmlspecialchars($shop['bn_description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="owner_name" class="form-label">Owner Name *</label>
                                <input type="text" class="form-control" id="owner_name" name="owner_name" 
                                       value="<?php echo htmlspecialchars($shop['owner_name']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($shop['phone']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($shop['email'] ?? ''); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Address *</label>
                        <textarea class="form-control" id="address" name="address" rows="2" required><?php echo htmlspecialchars($shop['address']); ?></textarea>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Shop Status
                </h5>
            </div>
            <div class="card-body">
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Verification Status:</span>
                        <?php if ($shop['is_verified']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Verified
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>Pending
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Shop Status:</span>
                        <?php if ($shop['is_active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-times-circle me-1"></i>Inactive
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Rating:</span>
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-star me-1"></i><?php echo number_format($shop['rating'], 1); ?>
                        </span>
                    </div>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Total Reviews:</span>
                        <span class="badge bg-info"><?php echo number_format($shop['total_reviews']); ?></span>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Commission Rate:</span>
                        <span class="badge bg-secondary"><?php echo number_format($shop['commission_percentage'], 1); ?>%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>Location
                </h5>
            </div>
            <div class="card-body">
                <div class="location-info">
                    <p class="mb-2">
                        <strong>Division:</strong> <?php echo htmlspecialchars($shop['division_name'] ?? 'N/A'); ?>
                    </p>
                    <p class="mb-2">
                        <strong>District:</strong> <?php echo htmlspecialchars($shop['district_name'] ?? 'N/A'); ?>
                    </p>
                    <p class="mb-2">
                        <strong>Upazilla:</strong> <?php echo htmlspecialchars($shop['upazilla_name'] ?? 'N/A'); ?>
                    </p>
                    <p class="mb-0">
                        <strong>Coordinates:</strong><br>
                        <small class="text-muted">
                            Lat: <?php echo $shop['latitude']; ?><br>
                            Lng: <?php echo $shop['longitude']; ?>
                        </small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>Dates
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>Created:</strong><br>
                    <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($shop['created_at'])); ?></small>
                </p>
                <p class="mb-0">
                    <strong>Last Updated:</strong><br>
                    <small class="text-muted"><?php echo date('M j, Y g:i A', strtotime($shop['updated_at'])); ?></small>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.status-item {
    padding: 8px 0;
    border-bottom: 1px solid #e3e6f0;
}

.status-item:last-child {
    border-bottom: none;
}

.location-info p {
    font-size: 0.9rem;
}

.card-header {
    background: linear-gradient(135deg, var(--shop-primary) 0%, var(--shop-secondary) 100%);
    color: white;
}
</style>

<?php include 'includes/footer.php'; ?>
