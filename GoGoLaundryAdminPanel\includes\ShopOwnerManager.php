<?php
/**
 * Shop Owner Manager Class
 *
 * This class handles shop owner authentication and management operations
 */

class ShopOwnerManager {
    private $pdo;
    private $maxLoginAttempts = 5;
    private $lockoutDuration = 30; // minutes

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Create a new shop owner account
     *
     * @param int $shopId Shop ID
     * @param string $username Username
     * @param string $password Password
     * @param string $email Email
     * @param string $phone Phone number
     * @param string $fullName Full name
     * @return int|bool Shop owner ID on success, false on failure
     */
    public function createShopOwner($shopId, $username, $password, $email, $phone, $fullName) {
        try {
            // Check if shop exists
            $shopStmt = $this->pdo->prepare("SELECT id FROM laundry_shops WHERE id = ?");
            $shopStmt->execute([$shopId]);
            if (!$shopStmt->fetch()) {
                return false;
            }

            // Check if username already exists
            if ($this->shopOwnerExistsByUsername($username)) {
                return false;
            }

            // Check if shop already has an owner
            if ($this->shopOwnerExistsByShopId($shopId)) {
                return false;
            }

            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Insert shop owner
            $stmt = $this->pdo->prepare("
                INSERT INTO shop_owners (shop_id, username, password, email, phone, full_name, is_active, is_verified)
                VALUES (?, ?, ?, ?, ?, ?, 1, 0)
            ");

            if ($stmt->execute([$shopId, $username, $hashedPassword, $email, $phone, $fullName])) {
                return $this->pdo->lastInsertId();
            }

            return false;
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::createShopOwner - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Authenticate shop owner
     *
     * @param string $username Username
     * @param string $password Password
     * @return array|string|bool Shop owner data on success, 'locked' if account is locked, false on failure
     */
    public function login($username, $password) {
        try {
            // Get shop owner
            $shopOwner = $this->getShopOwnerByUsername($username);

            if (!$shopOwner) {
                return false;
            }

            // Check if account is locked
            if ($shopOwner['locked_until'] !== null && strtotime($shopOwner['locked_until']) > time()) {
                return 'locked';
            }

            // Verify password
            if (password_verify($password, $shopOwner['password'])) {
                // Reset login attempts
                $this->resetLoginAttempts($shopOwner['id']);

                // Update last login time
                $this->updateLastLogin($shopOwner['id']);

                // Remove password from shop owner data
                unset($shopOwner['password']);
                return $shopOwner;
            }

            // Increment login attempts
            $this->incrementLoginAttempts($shopOwner['id']);

            // Check if account should be locked
            if ($shopOwner['login_attempts'] + 1 >= $this->maxLoginAttempts) {
                $this->lockAccount($shopOwner['id']);
                return 'locked';
            }

            return false;
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::login - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get shop owner by username
     *
     * @param string $username Username
     * @return array|bool Shop owner data on success, false on failure
     */
    public function getShopOwnerByUsername($username) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT so.*, ls.name as shop_name, ls.is_active as shop_is_active, ls.is_verified as shop_is_verified
                FROM shop_owners so
                LEFT JOIN laundry_shops ls ON so.shop_id = ls.id
                WHERE so.username = ?
            ");
            $stmt->execute([$username]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::getShopOwnerByUsername - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get shop owner by ID
     *
     * @param int $id Shop owner ID
     * @return array|bool Shop owner data on success, false on failure
     */
    public function getShopOwnerById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT so.*, ls.name as shop_name, ls.is_active as shop_is_active, ls.is_verified as shop_is_verified
                FROM shop_owners so
                LEFT JOIN laundry_shops ls ON so.shop_id = ls.id
                WHERE so.id = ?
            ");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::getShopOwnerById - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get shop owner by shop ID
     *
     * @param int $shopId Shop ID
     * @return array|bool Shop owner data on success, false on failure
     */
    public function getShopOwnerByShopId($shopId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT so.*, ls.name as shop_name, ls.is_active as shop_is_active, ls.is_verified as shop_is_verified
                FROM shop_owners so
                LEFT JOIN laundry_shops ls ON so.shop_id = ls.id
                WHERE so.shop_id = ?
            ");
            $stmt->execute([$shopId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::getShopOwnerByShopId - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if shop owner exists by username
     *
     * @param string $username Username
     * @return bool True if exists, false otherwise
     */
    public function shopOwnerExistsByUsername($username) {
        try {
            $stmt = $this->pdo->prepare("SELECT id FROM shop_owners WHERE username = ?");
            $stmt->execute([$username]);
            return $stmt->fetch() !== false;
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::shopOwnerExistsByUsername - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if shop owner exists by shop ID
     *
     * @param int $shopId Shop ID
     * @return bool True if exists, false otherwise
     */
    public function shopOwnerExistsByShopId($shopId) {
        try {
            $stmt = $this->pdo->prepare("SELECT id FROM shop_owners WHERE shop_id = ?");
            $stmt->execute([$shopId]);
            return $stmt->fetch() !== false;
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::shopOwnerExistsByShopId - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify shop owner account
     *
     * @param int $shopOwnerId Shop owner ID
     * @return bool True on success, false on failure
     */
    public function verifyShopOwner($shopOwnerId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE shop_owners SET is_verified = 1 WHERE id = ?");
            return $stmt->execute([$shopOwnerId]);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::verifyShopOwner - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update shop owner password
     *
     * @param int $shopOwnerId Shop owner ID
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function updatePassword($shopOwnerId, $newPassword) {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("UPDATE shop_owners SET password = ? WHERE id = ?");
            return $stmt->execute([$hashedPassword, $shopOwnerId]);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::updatePassword - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Reset login attempts
     *
     * @param int $shopOwnerId Shop owner ID
     * @return bool True on success, false on failure
     */
    private function resetLoginAttempts($shopOwnerId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE shop_owners SET login_attempts = 0, locked_until = NULL WHERE id = ?");
            return $stmt->execute([$shopOwnerId]);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::resetLoginAttempts - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Increment login attempts
     *
     * @param int $shopOwnerId Shop owner ID
     * @return bool True on success, false on failure
     */
    private function incrementLoginAttempts($shopOwnerId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE shop_owners SET login_attempts = login_attempts + 1 WHERE id = ?");
            return $stmt->execute([$shopOwnerId]);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::incrementLoginAttempts - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Lock account
     *
     * @param int $shopOwnerId Shop owner ID
     * @return bool True on success, false on failure
     */
    private function lockAccount($shopOwnerId) {
        try {
            $lockUntil = date('Y-m-d H:i:s', time() + ($this->lockoutDuration * 60));
            $stmt = $this->pdo->prepare("UPDATE shop_owners SET locked_until = ? WHERE id = ?");
            return $stmt->execute([$lockUntil, $shopOwnerId]);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::lockAccount - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update last login time
     *
     * @param int $shopOwnerId Shop owner ID
     * @return bool True on success, false on failure
     */
    private function updateLastLogin($shopOwnerId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE shop_owners SET last_login = NOW() WHERE id = ?");
            return $stmt->execute([$shopOwnerId]);
        } catch (PDOException $e) {
            error_log('ShopOwnerManager::updateLastLogin - Database error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create CSRF token
     *
     * @return string CSRF token
     */
    public function createCsrfToken() {
        return bin2hex(random_bytes(32));
    }

    /**
     * Verify CSRF token
     *
     * @param string $token CSRF token
     * @return bool True if token is valid, false otherwise
     */
    public function verifyCsrfToken($token) {
        // For debugging purposes, always return true if in development environment
        if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
            return true;
        }

        // Check if token exists and matches
        if (!isset($_SESSION['shop_csrf_token'])) {
            error_log('Shop CSRF token not found in session');
            return false;
        }

        if ($_SESSION['shop_csrf_token'] !== $token) {
            error_log('Shop CSRF token mismatch. Session: ' . $_SESSION['shop_csrf_token'] . ', Provided: ' . $token);
            return false;
        }

        return true;
    }
}
