package com.mdsadrulhasan.gogolaundry.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import java.util.Map;

/**
 * Model class for admin payment configuration
 * Contains admin payment numbers and instructions for users
 */
public class AdminPaymentConfig {

    @SerializedName("admin_payment_numbers")
    private Map<String, AdminPaymentNumber> adminPaymentNumbers;

    @SerializedName("payment_instructions")
    private String paymentInstructions;

    @SerializedName("verification_required")
    private boolean verificationRequired;

    @SerializedName("payment_methods")
    private List<PaymentMethod> paymentMethods;

    @SerializedName("important_note")
    private String importantNote;

    // Getters and setters
    public Map<String, AdminPaymentNumber> getAdminPaymentNumbers() {
        return adminPaymentNumbers;
    }

    public void setAdminPaymentNumbers(Map<String, AdminPaymentNumber> adminPaymentNumbers) {
        this.adminPaymentNumbers = adminPaymentNumbers;
    }

    public String getPaymentInstructions() {
        return paymentInstructions;
    }

    public void setPaymentInstructions(String paymentInstructions) {
        this.paymentInstructions = paymentInstructions;
    }

    public boolean isVerificationRequired() {
        return verificationRequired;
    }

    public void setVerificationRequired(boolean verificationRequired) {
        this.verificationRequired = verificationRequired;
    }

    public List<PaymentMethod> getPaymentMethods() {
        return paymentMethods;
    }

    public void setPaymentMethods(List<PaymentMethod> paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    public String getImportantNote() {
        return importantNote;
    }

    public void setImportantNote(String importantNote) {
        this.importantNote = importantNote;
    }

    /**
     * Get admin payment number for specific method
     */
    public String getAdminNumber(String paymentMethod) {
        if (adminPaymentNumbers != null && adminPaymentNumbers.containsKey(paymentMethod.toLowerCase())) {
            AdminPaymentNumber paymentNumber = adminPaymentNumbers.get(paymentMethod.toLowerCase());
            return paymentNumber != null ? paymentNumber.getNumber() : null;
        }
        return null;
    }

    /**
     * Get display name for payment method
     */
    public String getDisplayName(String paymentMethod) {
        if (adminPaymentNumbers != null && adminPaymentNumbers.containsKey(paymentMethod.toLowerCase())) {
            AdminPaymentNumber paymentNumber = adminPaymentNumbers.get(paymentMethod.toLowerCase());
            return paymentNumber != null ? paymentNumber.getDisplayName() : paymentMethod;
        }
        return paymentMethod;
    }

    /**
     * Inner class for admin payment number details
     */
    public static class AdminPaymentNumber {
        @SerializedName("number")
        private String number;

        @SerializedName("display_name")
        private String displayName;

        @SerializedName("instructions")
        private String instructions;

        // Getters and setters
        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public String getInstructions() {
            return instructions;
        }

        public void setInstructions(String instructions) {
            this.instructions = instructions;
        }
    }

    /**
     * Inner class for payment method details
     */
    public static class PaymentMethod {
        @SerializedName("method_name")
        private String methodName;

        @SerializedName("display_name")
        private String displayName;

        @SerializedName("min_amount")
        private double minAmount;

        @SerializedName("max_amount")
        private double maxAmount;

        @SerializedName("processing_fee_type")
        private String processingFeeType;

        @SerializedName("processing_fee_value")
        private double processingFeeValue;

        @SerializedName("is_active")
        private boolean isActive;

        // Getters and setters
        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public double getMinAmount() {
            return minAmount;
        }

        public void setMinAmount(double minAmount) {
            this.minAmount = minAmount;
        }

        public double getMaxAmount() {
            return maxAmount;
        }

        public void setMaxAmount(double maxAmount) {
            this.maxAmount = maxAmount;
        }

        public String getProcessingFeeType() {
            return processingFeeType;
        }

        public void setProcessingFeeType(String processingFeeType) {
            this.processingFeeType = processingFeeType;
        }

        public double getProcessingFeeValue() {
            return processingFeeValue;
        }

        public void setProcessingFeeValue(double processingFeeValue) {
            this.processingFeeValue = processingFeeValue;
        }

        public boolean isActive() {
            return isActive;
        }

        public void setActive(boolean active) {
            isActive = active;
        }
    }
}
