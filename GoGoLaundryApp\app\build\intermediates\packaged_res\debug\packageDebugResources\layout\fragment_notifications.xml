<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="6dp"
        android:paddingTop="6dp"
        android:paddingEnd="6dp"
        android:paddingBottom="16dp">

        <!-- Header Section with Glass Effect -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/notifications_header_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_hero_background"
            android:elevation="8dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Header Title with Icon -->
            <LinearLayout
                android:id="@+id/notifications_title_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_notification"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Notifications"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/home_text_on_gradient"
                    android:textStyle="bold"
                    android:letterSpacing="0.02" />

                <!-- Filter Toggle Button -->
                <FrameLayout
                    android:id="@+id/filter_toggle_container"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/glass_button_background"
                    android:elevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackgroundBorderless">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_settings"
                        app:tint="@color/home_accent_blue" />

                </FrameLayout>

            </LinearLayout>

            <!-- Filter Controls -->
            <FrameLayout
                android:id="@+id/filter_controls_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/notifications_title_container">

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/show_read_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Show Read Notifications"
                    android:textColor="@color/home_text_on_gradient"
                    android:textSize="14sp"
                    app:thumbTint="@color/home_accent_blue"
                    app:trackTint="@color/glass_border" />

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Notifications Content Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/notifications_content_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/notifications_header_section">

            <!-- Section Title -->
            <LinearLayout
                android:id="@+id/notifications_content_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_receipt"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Recent Notifications"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

                <!-- Refresh Button -->
                <FrameLayout
                    android:id="@+id/refresh_button"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="@drawable/glass_button_background"
                    android:elevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackgroundBorderless">

                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_arrow_right"
                        android:rotation="90"
                        app:tint="@color/home_accent_blue" />

                </FrameLayout>

            </LinearLayout>

            <!-- Notifications List Container -->
            <FrameLayout
                android:id="@+id/notifications_list_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:minHeight="200dp"
                app:layout_constraintTop_toBottomOf="@id/notifications_content_header">

                <!-- SwipeRefreshLayout -->
                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/notifications_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:nestedScrollingEnabled="false"
                        android:paddingStart="16dp"
                        android:paddingTop="16dp"
                        android:paddingEnd="16dp"
                        android:paddingBottom="16dp"
                        android:scrollbars="vertical"
                        android:visibility="visible"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_notification" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

                <!-- Loading Progress -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    style="?android:attr/progressBarStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:indeterminateTint="@color/home_accent_blue"
                    android:visibility="gone" />

                <!-- Empty State -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/empty_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:padding="32dp"
                    android:visibility="gone">

                    <!-- Empty State Icon with Glass Background -->
                    <FrameLayout
                        android:id="@+id/empty_icon_container"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:background="@drawable/glass_icon_background"
                        android:elevation="4dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_notification"
                            android:alpha="0.7"
                            app:tint="@color/home_accent_blue" />

                    </FrameLayout>

                    <!-- Empty State Title -->
                    <TextView
                        android:id="@+id/empty_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="No Notifications"
                        android:textAlignment="center"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:alpha="0.9"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/empty_icon_container" />

                    <!-- Empty State Subtitle -->
                    <TextView
                        android:id="@+id/empty_subtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="You're all caught up! No new notifications at the moment."
                        android:textAlignment="center"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                        android:textColor="@color/text_secondary"
                        android:alpha="0.8"
                        android:lineSpacingExtra="2dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/empty_title" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
