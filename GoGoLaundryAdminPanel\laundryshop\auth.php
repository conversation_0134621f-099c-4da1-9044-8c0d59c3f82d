<?php
/**
 * Shop Owner Authentication Middleware
 *
 * This file checks if the shop owner is logged in and redirects to the login page if not
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ShopOwnerManager.php';

// Check if shop owner is logged in
if (!isset($_SESSION['shop_owner_id']) || !isset($_SESSION['shop_owner_logged_in']) || $_SESSION['shop_owner_logged_in'] !== true) {
    // Store the requested URL for redirection after login
    $_SESSION['shop_owner_redirect'] = $_SERVER['REQUEST_URI'];

    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Initialize shop owner manager
$shopOwnerManager = new ShopOwnerManager($pdo);

// Get shop owner data
$shopOwner = $shopOwnerManager->getShopOwnerById($_SESSION['shop_owner_id']);

// Check if shop owner exists and is active
if (!$shopOwner || !$shopOwner['is_active']) {
    // Clear session
    session_unset();
    session_destroy();

    // Redirect to login page
    header('Location: login.php?error=account_inactive');
    exit;
}

// Check if shop is active and verified
if (!$shopOwner['shop_is_active'] || !$shopOwner['shop_is_verified']) {
    // Store shop owner data for use in templates
    $shopOwnerData = [
        'id' => $shopOwner['id'],
        'username' => $shopOwner['username'],
        'email' => $shopOwner['email'],
        'full_name' => $shopOwner['full_name'],
        'phone' => $shopOwner['phone'],
        'shop_id' => $shopOwner['shop_id'],
        'shop_name' => $shopOwner['shop_name'],
        'is_active' => $shopOwner['is_active'],
        'is_verified' => $shopOwner['is_verified'],
        'shop_is_active' => $shopOwner['shop_is_active'],
        'shop_is_verified' => $shopOwner['shop_is_verified'],
        'last_login' => $shopOwner['last_login']
    ];

    // Redirect to pending approval page
    if (!$shopOwner['shop_is_verified']) {
        header('Location: pending_approval.php');
        exit;
    } elseif (!$shopOwner['shop_is_active']) {
        header('Location: shop_inactive.php');
        exit;
    }
}

// Create CSRF token if not exists
if (!isset($_SESSION['shop_csrf_token'])) {
    $_SESSION['shop_csrf_token'] = $shopOwnerManager->createCsrfToken();
}

// Set shop owner data for use in templates
$shopOwnerData = [
    'id' => $shopOwner['id'],
    'username' => $shopOwner['username'],
    'email' => $shopOwner['email'],
    'full_name' => $shopOwner['full_name'],
    'phone' => $shopOwner['phone'],
    'shop_id' => $shopOwner['shop_id'],
    'shop_name' => $shopOwner['shop_name'],
    'is_active' => $shopOwner['is_active'],
    'is_verified' => $shopOwner['is_verified'],
    'shop_is_active' => $shopOwner['shop_is_active'],
    'shop_is_verified' => $shopOwner['shop_is_verified'],
    'last_login' => $shopOwner['last_login']
];
