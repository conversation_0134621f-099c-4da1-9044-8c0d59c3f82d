<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:elevation="8dp"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="@color/glass_background"
    app:strokeColor="@color/white_20"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Shop Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Shop Image Placeholder -->
            <View
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_marginEnd="12dp"
                android:background="@color/white_10" />

            <!-- Shop Info Placeholders -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Shop Name Placeholder -->
                <View
                    android:layout_width="150dp"
                    android:layout_height="16dp"
                    android:background="@color/white_20" />

                <!-- Shop Address Placeholder -->
                <View
                    android:layout_width="120dp"
                    android:layout_height="12dp"
                    android:layout_marginTop="4dp"
                    android:background="@color/white_10" />

                <!-- Rating Placeholder -->
                <View
                    android:layout_width="80dp"
                    android:layout_height="12dp"
                    android:layout_marginTop="4dp"
                    android:background="@color/white_10" />

            </LinearLayout>

            <!-- Status Badge Placeholder -->
            <View
                android:layout_width="50dp"
                android:layout_height="20dp"
                android:background="@color/white_10" />

        </LinearLayout>

        <!-- Services Placeholder -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <View
                android:layout_width="60dp"
                android:layout_height="12dp"
                android:background="@color/white_10" />

            <View
                android:layout_width="80dp"
                android:layout_height="20dp"
                android:layout_marginStart="8dp"
                android:background="@color/white_10" />

            <View
                android:layout_width="70dp"
                android:layout_height="20dp"
                android:layout_marginStart="4dp"
                android:background="@color/white_10" />

        </LinearLayout>

        <!-- Action Buttons Placeholder -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <View
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:background="@color/white_10" />

            <View
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:background="@color/white_10" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
