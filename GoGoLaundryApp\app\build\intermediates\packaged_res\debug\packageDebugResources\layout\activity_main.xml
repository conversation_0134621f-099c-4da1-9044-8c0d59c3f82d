<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/main"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        tools:context=".MainActivity">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/signupCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardUseCompatPadding="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.5">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <!-- Optional Title -->
                <TextView
                    android:id="@+id/textViewTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Create Account"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginBottom="24dp"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldFullNameLayout"/>

                <!-- Full Name -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldFullNameLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Full Name"
                    app:startIconDrawable="@drawable/ic_person"
                    app:layout_constraintTop_toBottomOf="@id/textViewTitle"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldPhoneLayout"
                    android:layout_marginBottom="12dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextFullName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName|textCapWords"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Phone Number -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldPhoneLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Phone Number"
                    app:startIconDrawable="@drawable/ic_phone"
                    app:layout_constraintTop_toBottomOf="@id/textFieldFullNameLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldEmailLayout"
                    android:layout_marginBottom="12dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Email (Optional) -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldEmailLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Email (Optional)"
                    app:startIconDrawable="@drawable/ic_email"
                    app:layout_constraintTop_toBottomOf="@id/textFieldPhoneLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldPasswordLayout"
                    android:layout_marginBottom="12dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Password -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldPasswordLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Password"
                    app:startIconDrawable="@drawable/ic_lock"
                    app:endIconMode="password_toggle"
                    app:layout_constraintTop_toBottomOf="@id/textFieldEmailLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldConfirmPasswordLayout"
                    android:layout_marginBottom="12dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Confirm Password -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldConfirmPasswordLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Confirm Password"
                    app:startIconDrawable="@drawable/ic_lock"
                    app:endIconMode="password_toggle"
                    app:layout_constraintTop_toBottomOf="@id/textFieldPasswordLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldDivisionLayout"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextConfirmPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Division (Department) Dropdown -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldDivisionLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Division (Department)"
                    app:layout_constraintTop_toBottomOf="@id/textFieldConfirmPasswordLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldDistrictLayout"
                    android:layout_marginBottom="12dp">

                    <AutoCompleteTextView
                        android:id="@+id/autoCompleteDivision"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:labelFor="@id/textFieldDivisionLayout"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- District Dropdown -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldDistrictLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="District"
                    app:layout_constraintTop_toBottomOf="@id/textFieldDivisionLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textFieldUpazilaLayout"
                    android:layout_marginBottom="12dp">

                    <AutoCompleteTextView
                        android:id="@+id/autoCompleteDistrict"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:labelFor="@id/textFieldDistrictLayout"
                        android:enabled="false"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Upazila Dropdown -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textFieldUpazilaLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="Upazila"
                    app:layout_constraintTop_toBottomOf="@id/textFieldDistrictLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/buttonSignUp"
                    android:layout_marginBottom="24dp">

                    <AutoCompleteTextView
                        android:id="@+id/autoCompleteUpazila"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:labelFor="@id/textFieldUpazilaLayout"
                        android:enabled="false"  />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Loading Indicator for Upazila -->
                <ProgressBar
                    android:id="@+id/progressBarUpazila"
                    style="?android:attr/progressBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toEndOf="@id/textFieldUpazilaLayout"
                    app:layout_constraintTop_toTopOf="@id/textFieldUpazilaLayout"
                    app:layout_constraintBottom_toBottomOf="@id/textFieldUpazilaLayout"
                    android:layout_marginEnd="50dp"/>

                <!-- Sign Up Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonSignUp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Sign Up"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    app:layout_constraintTop_toBottomOf="@id/textFieldUpazilaLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/textViewSelectedData"
                    android:layout_marginBottom="16dp"/>

                <!-- Selected Data Display -->
                <TextView
                    android:id="@+id/textViewSelectedData"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@android:color/darker_gray"
                    android:padding="12dp"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    tools:text="Selected data will appear here"
                    app:layout_constraintTop_toBottomOf="@id/buttonSignUp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>