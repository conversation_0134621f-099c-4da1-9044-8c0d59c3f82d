<div class="sidebar">
    <div class="sidebar-header">
        <h3><i class="fas fa-store me-2"></i><?php echo APP_NAME; ?></h3>
        <div class="sidebar-subtitle">Shop Owner Panel</div>
        <!-- Mobile close button -->
        <button type="button" class="btn-close btn-close-white d-md-none position-absolute top-0 end-0 mt-2 me-2" id="closeSidebarMobile" aria-label="Close"></button>
    </div>

    <ul class="list-unstyled components" id="sidebarMenu">
        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">
            <a href="index.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>

        <!-- Orders Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['orders.php', 'order_details.php']) ? 'active' : ''; ?>">
            <a href="#ordersSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-shopping-cart"></i> Orders
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['orders.php', 'order_details.php']) ? 'show' : ''; ?>" id="ordersSubmenu">
                <li>
                    <a href="orders.php">
                        <i class="fas fa-list"></i> All Orders
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=placed">
                        <i class="fas fa-clock"></i> New Orders
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=processing">
                        <i class="fas fa-spinner"></i> Processing
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=out_for_delivery">
                        <i class="fas fa-truck"></i> Out for Delivery
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=delivered">
                        <i class="fas fa-check-circle"></i> Completed
                    </a>
                </li>
            </ul>
        </li>

        <!-- Services & Items Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['services.php', 'items.php', 'add_service.php', 'edit_service.php', 'add_item.php', 'edit_item.php']) ? 'active' : ''; ?>">
            <a href="#servicesSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-tags"></i> Services & Items
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['services.php', 'items.php', 'add_service.php', 'edit_service.php', 'add_item.php', 'edit_item.php']) ? 'show' : ''; ?>" id="servicesSubmenu">
                <li>
                    <a href="services.php">
                        <i class="fas fa-concierge-bell"></i> My Services
                    </a>
                </li>
                <li>
                    <a href="items.php">
                        <i class="fas fa-tshirt"></i> My Items
                    </a>
                </li>
                <li>
                    <a href="pricing.php">
                        <i class="fas fa-dollar-sign"></i> Pricing Management
                    </a>
                </li>
            </ul>
        </li>

        <!-- Shop Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['profile.php', 'shop_settings.php']) ? 'active' : ''; ?>">
            <a href="#shopSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-store"></i> Shop Management
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['profile.php', 'shop_settings.php']) ? 'show' : ''; ?>" id="shopSubmenu">
                <li>
                    <a href="profile.php">
                        <i class="fas fa-info-circle"></i> Shop Profile
                    </a>
                </li>
                <li>
                    <a href="shop_settings.php">
                        <i class="fas fa-cogs"></i> Shop Settings
                    </a>
                </li>
            </ul>
        </li>

        <!-- Reports -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['reports.php', 'sales_report.php', 'order_report.php']) ? 'active' : ''; ?>">
            <a href="#reportsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-chart-bar"></i> Reports
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['reports.php', 'sales_report.php', 'order_report.php']) ? 'show' : ''; ?>" id="reportsSubmenu">
                <li>
                    <a href="sales_report.php">
                        <i class="fas fa-chart-line"></i> Sales Report
                    </a>
                </li>
                <li>
                    <a href="order_report.php">
                        <i class="fas fa-chart-pie"></i> Order Analytics
                    </a>
                </li>
            </ul>
        </li>

        <!-- Payments & Withdrawals -->
        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'withdrawal_requests.php' ? 'active' : ''; ?>">
            <a href="withdrawal_requests.php">
                <i class="fas fa-money-bill-wave"></i> Withdrawal Requests
            </a>
        </li>

        <!-- Account Settings -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['account.php', 'change_password.php']) ? 'active' : ''; ?>">
            <a href="#accountSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-user-cog"></i> Account
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['account.php', 'change_password.php']) ? 'show' : ''; ?>" id="accountSubmenu">
                <li>
                    <a href="account.php">
                        <i class="fas fa-user"></i> My Account
                    </a>
                </li>
                <li>
                    <a href="change_password.php">
                        <i class="fas fa-key"></i> Change Password
                    </a>
                </li>
            </ul>
        </li>

        <!-- Help & Support -->
        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'help.php' ? 'active' : ''; ?>">
            <a href="help.php">
                <i class="fas fa-question-circle"></i> Help & Support
            </a>
        </li>
    </ul>

    <!-- Shop Status Indicator -->
    <div class="sidebar-footer">
        <div class="shop-status mb-3">
            <div class="text-center text-white">
                <small class="d-block mb-1">Shop Status</small>
                <?php if ($shopOwnerData['shop_is_verified'] && $shopOwnerData['shop_is_active']): ?>
                    <span class="badge bg-success">
                        <i class="fas fa-check-circle me-1"></i>Active & Verified
                    </span>
                <?php elseif ($shopOwnerData['shop_is_verified'] && !$shopOwnerData['shop_is_active']): ?>
                    <span class="badge bg-warning">
                        <i class="fas fa-pause-circle me-1"></i>Verified but Inactive
                    </span>
                <?php else: ?>
                    <span class="badge bg-danger">
                        <i class="fas fa-clock me-1"></i>Pending Verification
                    </span>
                <?php endif; ?>
            </div>
        </div>

        <a href="logout.php" class="btn btn-danger btn-sm w-100">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>

<script>
// Sidebar toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    const closeSidebarMobile = document.getElementById('closeSidebarMobile');
    const sidebar = document.querySelector('.sidebar');
    const content = document.querySelector('.content');

    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            content.classList.toggle('active');
        });
    }

    if (closeSidebarMobile) {
        closeSidebarMobile.addEventListener('click', function() {
            sidebar.classList.remove('active');
            content.classList.remove('active');
        });
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarCollapse.contains(e.target)) {
                sidebar.classList.remove('active');
                content.classList.remove('active');
            }
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
            content.classList.remove('active');
        }
    });
});
</script>

<style>
.sidebar {
    min-width: 250px;
    max-width: 250px;
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    transition: all 0.3s;
}

.sidebar.active {
    margin-left: -250px;
}

.content {
    width: 100%;
    padding: 0;
    min-height: 100vh;
    transition: all 0.3s;
    margin-left: 250px;
}

.content.active {
    margin-left: 0;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
    color: white;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    margin-top: 5px;
}

.sidebar ul.components {
    padding: 20px 0;
}

.sidebar ul li {
    margin-bottom: 5px;
}

.sidebar ul li a {
    padding: 12px 20px;
    font-size: 0.9rem;
    display: block;
    text-decoration: none;
    border-radius: 0;
}

.sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar ul ul {
    background: rgba(0, 0, 0, 0.1);
    padding: 0;
}

.sidebar ul ul li a {
    padding: 10px 40px;
    font-size: 0.85rem;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
}

.shop-status {
    text-align: center;
}

@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
    }

    .sidebar.active {
        margin-left: 0;
    }

    .content {
        margin-left: 0;
    }

    .content.active {
        margin-left: 0;
    }
}
</style>
