<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Sales Report';
$currentPage = 'sales_report';

// Get date range parameters
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // First day of current month
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d'); // Today
$period = isset($_GET['period']) ? $_GET['period'] : 'custom';

// Set date range based on period
switch ($period) {
    case 'today':
        $startDate = $endDate = date('Y-m-d');
        break;
    case 'yesterday':
        $startDate = $endDate = date('Y-m-d', strtotime('-1 day'));
        break;
    case 'this_week':
        $startDate = date('Y-m-d', strtotime('monday this week'));
        $endDate = date('Y-m-d');
        break;
    case 'last_week':
        $startDate = date('Y-m-d', strtotime('monday last week'));
        $endDate = date('Y-m-d', strtotime('sunday last week'));
        break;
    case 'this_month':
        $startDate = date('Y-m-01');
        $endDate = date('Y-m-d');
        break;
    case 'last_month':
        $startDate = date('Y-m-01', strtotime('last month'));
        $endDate = date('Y-m-t', strtotime('last month'));
        break;
}

// Get sales data
try {
    // Overall statistics
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total ELSE 0 END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN status = 'delivered' THEN total ELSE NULL END), 0) as avg_order_value,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total * (commission_percentage / 100) ELSE 0 END), 0) as commission_paid
        FROM orders o
        LEFT JOIN laundry_shops ls ON o.shop_id = ls.id
        WHERE o.shop_id = ? AND DATE(o.created_at) BETWEEN ? AND ?
    ");
    $statsStmt->execute([$shopOwnerData['shop_id'], $startDate, $endDate]);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

    // Daily sales data for chart
    $dailySalesStmt = $pdo->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as orders,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total ELSE 0 END), 0) as revenue
        FROM orders 
        WHERE shop_id = ? AND DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY DATE(created_at)
    ");
    $dailySalesStmt->execute([$shopOwnerData['shop_id'], $startDate, $endDate]);
    $dailySales = $dailySalesStmt->fetchAll(PDO::FETCH_ASSOC);

    // Top selling items
    $topItemsStmt = $pdo->prepare("
        SELECT 
            i.name as item_name,
            COUNT(oi.id) as quantity_sold,
            SUM(oi.price * oi.quantity) as total_revenue
        FROM order_items oi
        JOIN items i ON oi.item_id = i.id
        JOIN orders o ON oi.order_id = o.id
        WHERE o.shop_id = ? AND o.status = 'delivered' AND DATE(o.created_at) BETWEEN ? AND ?
        GROUP BY oi.item_id
        ORDER BY quantity_sold DESC
        LIMIT 10
    ");
    $topItemsStmt->execute([$shopOwnerData['shop_id'], $startDate, $endDate]);
    $topItems = $topItemsStmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log('Sales report error: ' . $e->getMessage());
    $stats = ['total_orders' => 0, 'completed_orders' => 0, 'total_revenue' => 0, 'avg_order_value' => 0, 'commission_paid' => 0];
    $dailySales = [];
    $topItems = [];
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Sales Report</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.print()">
            <i class="fas fa-print me-1"></i>Print Report
        </button>
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<!-- Date Range Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="period" class="form-label">Period</label>
                <select class="form-select" id="period" name="period" onchange="toggleCustomDates()">
                    <option value="today" <?= $period === 'today' ? 'selected' : '' ?>>Today</option>
                    <option value="yesterday" <?= $period === 'yesterday' ? 'selected' : '' ?>>Yesterday</option>
                    <option value="this_week" <?= $period === 'this_week' ? 'selected' : '' ?>>This Week</option>
                    <option value="last_week" <?= $period === 'last_week' ? 'selected' : '' ?>>Last Week</option>
                    <option value="this_month" <?= $period === 'this_month' ? 'selected' : '' ?>>This Month</option>
                    <option value="last_month" <?= $period === 'last_month' ? 'selected' : '' ?>>Last Month</option>
                    <option value="custom" <?= $period === 'custom' ? 'selected' : '' ?>>Custom Range</option>
                </select>
            </div>
            <div class="col-md-3" id="start_date_group">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $startDate ?>">
            </div>
            <div class="col-md-3" id="end_date_group">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $endDate ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">Generate Report</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Report Period Display -->
<div class="alert alert-info">
    <i class="fas fa-calendar me-2"></i>
    <strong>Report Period:</strong> <?= date('M j, Y', strtotime($startDate)) ?> to <?= date('M j, Y', strtotime($endDate)) ?>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($stats['total_orders']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                        <div class="h5 mb-0 font-weight-bold">৳<?= number_format($stats['total_revenue'], 2) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Completed Orders</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($stats['completed_orders']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Avg Order Value</div>
                        <div class="h5 mb-0 font-weight-bold">৳<?= number_format($stats['avg_order_value'], 2) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Daily Sales Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>Daily Sales Trend
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Top Selling Items -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>Top Selling Items
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($topItems)): ?>
                    <p class="text-muted text-center">No sales data available for this period.</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($topItems as $index => $item): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <div class="fw-bold"><?= htmlspecialchars($item['item_name']) ?></div>
                                    <small class="text-muted"><?= $item['quantity_sold'] ?> sold</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">৳<?= number_format($item['total_revenue'], 2) ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Commission Information -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-percentage me-2"></i>Commission Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="text-center">
                    <h4 class="text-danger">৳<?= number_format($stats['commission_paid'], 2) ?></h4>
                    <p class="text-muted mb-0">Commission Paid</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <h4 class="text-success">৳<?= number_format($stats['total_revenue'] - $stats['commission_paid'], 2) ?></h4>
                    <p class="text-muted mb-0">Your Earnings</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <h4 class="text-info"><?= $stats['total_revenue'] > 0 ? number_format(($stats['commission_paid'] / $stats['total_revenue']) * 100, 1) : 0 ?>%</h4>
                    <p class="text-muted mb-0">Commission Rate</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }

@media print {
    .btn-toolbar, .card-header .btn, .sidebar, .navbar { display: none !important; }
    .content { margin-left: 0 !important; }
}
</style>

<script>
// Toggle custom date inputs
function toggleCustomDates() {
    const period = document.getElementById('period').value;
    const startDateGroup = document.getElementById('start_date_group');
    const endDateGroup = document.getElementById('end_date_group');
    
    if (period === 'custom') {
        startDateGroup.style.display = 'block';
        endDateGroup.style.display = 'block';
    } else {
        startDateGroup.style.display = 'none';
        endDateGroup.style.display = 'none';
    }
}

// Initialize chart
document.addEventListener('DOMContentLoaded', function() {
    toggleCustomDates();
    
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesData = <?= json_encode($dailySales) ?>;
    
    const labels = salesData.map(item => {
        const date = new Date(item.date);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });
    
    const revenueData = salesData.map(item => parseFloat(item.revenue));
    const ordersData = salesData.map(item => parseInt(item.orders));
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Revenue (৳)',
                data: revenueData,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: 'Orders',
                data: ordersData,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Revenue (৳)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Orders'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
