<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/serviceCardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="@color/card_background_dark"
    app:strokeColor="@color/colorPrimary"
    app:strokeWidth="1dp"
    app:cardElevation="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp"
        android:gravity="center_vertical">

        <!-- Service Icon -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/serviceIcon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/gradient_blue_purple"
            android:padding="12dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_washing_machine"
            app:shapeAppearanceOverlay="@style/CircularImageView"
            app:tint="@color/white" />

        <!-- Service Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/serviceName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary_dark"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/kalpurush"
                android:layout_marginBottom="4dp"
                tools:text="Washing Service" />

            <TextView
                android:id="@+id/serviceDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary_dark"
                android:textSize="14sp"
                android:lineSpacingExtra="2dp"
                android:layout_marginBottom="8dp"
                tools:text="Professional washing with premium detergents" />

            <TextView
                android:id="@+id/servicePrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/price_tag_background"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                tools:text="From ৳50.00" />

        </LinearLayout>

        <!-- Arrow Icon -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_forward"
            app:tint="@color/colorPrimary" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
