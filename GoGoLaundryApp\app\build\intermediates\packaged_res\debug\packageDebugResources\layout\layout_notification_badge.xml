<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="4dp">

    <ImageView
        android:id="@+id/notification_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center"
        android:contentDescription="@string/notifications"
        android:src="@drawable/ic_notification" />

    <TextView
        android:id="@+id/notification_badge"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_gravity="top|end"
        android:background="@drawable/badge_background"
        android:gravity="center"
        android:text="0"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:visibility="gone" />

</FrameLayout>
