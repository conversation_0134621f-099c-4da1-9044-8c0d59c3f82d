<?php
/**
 * Shop Registration API Endpoint
 *
 * This endpoint handles shop owner registration
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ShopOwnerManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Validate required fields
$requiredFields = ['shop_name', 'owner_name', 'phone', 'address', 'latitude', 'longitude', 'username', 'password'];
foreach ($requiredFields as $field) {
    if (empty($inputData[$field])) {
        jsonResponse(false, ucfirst(str_replace('_', ' ', $field)) . ' is required', [], 400);
    }
}

// Sanitize input data
$shopName = sanitize($inputData['shop_name']);
$ownerName = sanitize($inputData['owner_name']);
$phone = sanitize($inputData['phone']);
$email = !empty($inputData['email']) ? sanitize($inputData['email']) : null;
$address = sanitize($inputData['address']);
$latitude = floatval($inputData['latitude']);
$longitude = floatval($inputData['longitude']);
$username = sanitize($inputData['username']);
$password = $inputData['password'];

// Optional fields
$divisionId = !empty($inputData['division_id']) ? intval($inputData['division_id']) : null;
$districtId = !empty($inputData['district_id']) ? intval($inputData['district_id']) : null;
$upazillaId = !empty($inputData['upazilla_id']) ? intval($inputData['upazilla_id']) : null;

// Validate data
if (strlen($password) < 8) {
    jsonResponse(false, 'Password must be at least 8 characters long', [], 400);
}

if ($latitude == 0 || $longitude == 0) {
    jsonResponse(false, 'Valid location coordinates are required', [], 400);
}

// Format phone number
$phone = formatPhone($phone);

// Validate email if provided
if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    jsonResponse(false, 'Invalid email format', [], 400);
}

try {
    // Start transaction
    $pdo->beginTransaction();

    // Check if phone number already exists
    $phoneCheckStmt = $pdo->prepare("SELECT id FROM laundry_shops WHERE phone = ?");
    $phoneCheckStmt->execute([$phone]);
    if ($phoneCheckStmt->fetch()) {
        $pdo->rollBack();
        jsonResponse(false, 'Phone number already registered', [], 400);
    }

    // Check if email already exists (if provided)
    if ($email) {
        $emailCheckStmt = $pdo->prepare("SELECT id FROM laundry_shops WHERE email = ?");
        $emailCheckStmt->execute([$email]);
        if ($emailCheckStmt->fetch()) {
            $pdo->rollBack();
            jsonResponse(false, 'Email already registered', [], 400);
        }
    }

    // Create shop first
    $shopStmt = $pdo->prepare("
        INSERT INTO laundry_shops (name, owner_name, phone, email, address, latitude, longitude, 
                                 division_id, district_id, upazilla_id, commission_percentage, 
                                 is_active, is_verified, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 15.00, 1, 0, NOW())
    ");

    if (!$shopStmt->execute([$shopName, $ownerName, $phone, $email, $address, $latitude, $longitude, 
                            $divisionId, $districtId, $upazillaId])) {
        $pdo->rollBack();
        jsonResponse(false, 'Failed to register shop', [], 500);
    }

    $shopId = $pdo->lastInsertId();

    // Create shop owner account
    $shopOwnerManager = new ShopOwnerManager($pdo);
    $shopOwnerId = $shopOwnerManager->createShopOwner($shopId, $username, $password, $email, $phone, $ownerName);

    if (!$shopOwnerId) {
        $pdo->rollBack();
        jsonResponse(false, 'Failed to create shop owner account. Username may already exist.', [], 400);
    }

    // Commit transaction
    $pdo->commit();

    // Get the created shop data
    $shopDataStmt = $pdo->prepare("
        SELECT ls.*, so.username, so.is_verified as owner_verified
        FROM laundry_shops ls
        LEFT JOIN shop_owners so ON ls.id = so.shop_id
        WHERE ls.id = ?
    ");
    $shopDataStmt->execute([$shopId]);
    $shopData = $shopDataStmt->fetch(PDO::FETCH_ASSOC);

    // Remove sensitive data
    unset($shopData['commission_percentage']);

    // Log the registration
    error_log("Shop registered successfully: Shop ID {$shopId}, Owner: {$ownerName}, Phone: {$phone}");

    // Return success response
    jsonResponse(true, 'Shop registration successful! Your shop has been submitted for admin approval.', [
        'shop' => $shopData,
        'message' => 'You will be notified once your shop is approved and verified by our admin team.'
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log('Shop registration error: ' . $e->getMessage());
    
    // Check for specific constraint violations
    if (strpos($e->getMessage(), 'phone') !== false) {
        jsonResponse(false, 'Phone number already registered', [], 400);
    } elseif (strpos($e->getMessage(), 'email') !== false) {
        jsonResponse(false, 'Email already registered', [], 400);
    } elseif (strpos($e->getMessage(), 'username') !== false) {
        jsonResponse(false, 'Username already exists', [], 400);
    } else {
        jsonResponse(false, 'Registration failed. Please try again later.', [], 500);
    }
} catch (Exception $e) {
    $pdo->rollBack();
    error_log('Shop registration error: ' . $e->getMessage());
    jsonResponse(false, 'Registration failed. Please try again later.', [], 500);
}
