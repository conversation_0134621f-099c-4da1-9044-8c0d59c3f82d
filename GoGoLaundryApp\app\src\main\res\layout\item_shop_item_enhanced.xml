<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/itemCardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="@color/card_background_dark"
    app:strokeColor="@color/colorPrimary"
    app:strokeWidth="1dp"
    app:cardElevation="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Item Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <!-- Item Icon -->
            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/itemIcon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/gradient_blue_purple"
                android:padding="10dp"
                android:scaleType="centerInside"
                android:src="@drawable/ic_laundry_item"
                app:shapeAppearanceOverlay="@style/CircularImageView"
                app:tint="@color/white" />

            <!-- Item Details -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/itemName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary_dark"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/kalpurush"
                    android:layout_marginBottom="4dp"
                    tools:text="T-Shirt" />

                <TextView
                    android:id="@+id/itemDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary_dark"
                    android:textSize="14sp"
                    android:lineSpacingExtra="2dp"
                    tools:text="Cotton T-shirt washing and ironing" />

            </LinearLayout>

            <!-- Price -->
            <TextView
                android:id="@+id/itemPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/colorPrimary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:background="@drawable/price_tag_background"
                android:paddingHorizontal="12dp"
                android:paddingVertical="6dp"
                tools:text="৳25.00" />

        </LinearLayout>

        <!-- Item Footer -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Estimated Time -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_clock"
                    app:tint="@color/text_secondary_dark"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/estimatedTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary_dark"
                    android:textSize="14sp"
                    tools:text="24 hours" />

            </LinearLayout>

            <!-- Add to Cart Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/addToCartButton"
                style="@style/Widget.Material3.Button"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="@string/add_to_cart"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                app:icon="@drawable/ic_add_shopping_cart"
                app:iconTint="@color/white"
                app:iconSize="18dp"
                app:backgroundTint="@color/colorPrimary"
                app:cornerRadius="20dp"
                android:paddingHorizontal="16dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
