<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Enhanced shadow for focused state -->
    <item android:top="3dp" android:left="3dp">
        <shape android:shape="rectangle">
            <solid android:color="#26000000" />
            <corners android:radius="28dp" />
        </shape>
    </item>
    
    <!-- Main glass background with enhanced gradient -->
    <item android:bottom="3dp" android:right="3dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/search_highlight"
                android:endColor="@color/search_background_primary"
                android:angle="135" />
            <corners android:radius="26dp" />
        </shape>
    </item>
    
    <!-- Accent border for focused state -->
    <item android:bottom="3dp" android:right="3dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="2dp" 
                android:color="@color/search_accent_blue" />
            <corners android:radius="26dp" />
        </shape>
    </item>
    
    <!-- Secondary border for depth -->
    <item android:top="1dp" android:left="1dp" android:right="4dp" android:bottom="4dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="@color/search_border_secondary" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    
    <!-- Inner glow effect -->
    <item android:top="2dp" android:left="2dp" android:right="5dp" android:bottom="5dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/search_background_secondary" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    
</layer-list>
