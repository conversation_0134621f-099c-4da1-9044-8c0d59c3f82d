<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'My Account';
$currentPage = 'account';

// Handle account updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_account':
            $fullName = sanitize($_POST['full_name']);
            $email = sanitize($_POST['email']);
            $phone = sanitize($_POST['phone']);
            
            try {
                $updateStmt = $pdo->prepare("
                    UPDATE shop_owners 
                    SET full_name = ?, email = ?, phone = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                if ($updateStmt->execute([$fullName, $email, $phone, $shopOwnerData['id']])) {
                    $_SESSION['shop_success_message'] = 'Account information updated successfully!';
                    // Update session data
                    $shopOwner = $shopOwnerManager->getShopOwnerById($shopOwnerData['id']);
                    $shopOwnerData = [
                        'id' => $shopOwner['id'],
                        'username' => $shopOwner['username'],
                        'email' => $shopOwner['email'],
                        'full_name' => $shopOwner['full_name'],
                        'phone' => $shopOwner['phone'],
                        'shop_id' => $shopOwner['shop_id'],
                        'shop_name' => $shopOwner['shop_name'],
                        'is_active' => $shopOwner['is_active'],
                        'is_verified' => $shopOwner['is_verified'],
                        'shop_is_active' => $shopOwner['shop_is_active'],
                        'shop_is_verified' => $shopOwner['shop_is_verified'],
                        'last_login' => $shopOwner['last_login']
                    ];
                } else {
                    $_SESSION['shop_error_message'] = 'Failed to update account information.';
                }
            } catch (PDOException $e) {
                error_log('Account update error: ' . $e->getMessage());
                if (strpos($e->getMessage(), 'email') !== false) {
                    $_SESSION['shop_error_message'] = 'Email already exists.';
                } elseif (strpos($e->getMessage(), 'phone') !== false) {
                    $_SESSION['shop_error_message'] = 'Phone number already exists.';
                } else {
                    $_SESSION['shop_error_message'] = 'Failed to update account information.';
                }
            }
            break;
    }
}

// Get account statistics
try {
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total ELSE 0 END), 0) as total_revenue,
            MIN(created_at) as first_order_date
        FROM orders 
        WHERE shop_id = ?
    ");
    $statsStmt->execute([$shopOwnerData['shop_id']]);
    $accountStats = $statsStmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $accountStats = ['total_orders' => 0, 'completed_orders' => 0, 'total_revenue' => 0, 'first_order_date' => null];
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Account</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<div class="row">
    <!-- Account Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Account Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_account">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" 
                                       value="<?php echo htmlspecialchars($shopOwnerData['username']); ?>" readonly>
                                <div class="form-text">Username cannot be changed</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($shopOwnerData['full_name']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($shopOwnerData['email'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($shopOwnerData['phone']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Account Information
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Settings -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Security Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="security-item">
                            <h6>Password</h6>
                            <p class="text-muted">Keep your account secure with a strong password</p>
                            <a href="change_password.php" class="btn btn-outline-primary">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="security-item">
                            <h6>Last Login</h6>
                            <p class="text-muted">
                                <?php if ($shopOwnerData['last_login']): ?>
                                    <?php echo date('M j, Y g:i A', strtotime($shopOwnerData['last_login'])); ?>
                                <?php else: ?>
                                    Never logged in
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Account Statistics -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Account Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Total Orders:</span>
                        <span class="fw-bold"><?php echo number_format($accountStats['total_orders']); ?></span>
                    </div>
                </div>
                
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Completed Orders:</span>
                        <span class="fw-bold text-success"><?php echo number_format($accountStats['completed_orders']); ?></span>
                    </div>
                </div>
                
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Total Revenue:</span>
                        <span class="fw-bold text-primary">৳<?php echo number_format($accountStats['total_revenue'], 2); ?></span>
                    </div>
                </div>
                
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Success Rate:</span>
                        <span class="fw-bold">
                            <?php 
                            $successRate = $accountStats['total_orders'] > 0 ? 
                                ($accountStats['completed_orders'] / $accountStats['total_orders']) * 100 : 0;
                            echo number_format($successRate, 1) . '%';
                            ?>
                        </span>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">First Order:</span>
                        <span class="fw-bold">
                            <?php 
                            echo $accountStats['first_order_date'] ? 
                                date('M j, Y', strtotime($accountStats['first_order_date'])) : 'No orders yet';
                            ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Status -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Account Status
                </h5>
            </div>
            <div class="card-body">
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Account Status:</span>
                        <?php if ($shopOwnerData['is_active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-times-circle me-1"></i>Inactive
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Verification:</span>
                        <?php if ($shopOwnerData['is_verified']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Verified
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>Pending
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Shop Status:</span>
                        <?php if ($shopOwnerData['shop_is_active']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Active
                            </span>
                        <?php else: ?>
                            <span class="badge bg-danger">
                                <i class="fas fa-times-circle me-1"></i>Inactive
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Shop Verification:</span>
                        <?php if ($shopOwnerData['shop_is_verified']): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i>Verified
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>Pending
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="profile.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-store me-2"></i>Edit Shop Profile
                    </a>
                    <a href="services.php" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-concierge-bell me-2"></i>Manage Services
                    </a>
                    <a href="items.php" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-tshirt me-2"></i>Manage Items
                    </a>
                    <a href="orders.php" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-shopping-cart me-2"></i>View Orders
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-item, .status-item {
    padding: 8px 0;
    border-bottom: 1px solid #e3e6f0;
}

.stat-item:last-child, .status-item:last-child {
    border-bottom: none;
}

.security-item {
    padding: 15px 0;
    border-bottom: 1px solid #e3e6f0;
}

.security-item:last-child {
    border-bottom: none;
}
</style>

<?php include 'includes/footer.php'; ?>
