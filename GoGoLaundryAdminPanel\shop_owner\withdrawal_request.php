<?php
/**
 * Shop Owner Withdrawal Request Page
 * 
 * Allows shop owners to submit withdrawal requests for their earnings
 */

// Include shop owner authentication
require_once 'auth.php';
require_once '../includes/PaymentWithdrawalManager.php';

$pageTitle = 'Withdrawal Request';
$currentPage = 'withdrawal';

// Initialize managers
$paymentWithdrawalManager = new PaymentWithdrawalManager($pdo);

// Get shop owner's balance
$balance = $paymentWithdrawalManager->getShopOwnerBalance($_SESSION['shop_id'], $_SESSION['shop_owner_id']);
if (!$balance) {
    // Initialize balance if not exists
    $stmt = $pdo->prepare("
        INSERT INTO shop_owner_earnings (shop_id, shop_owner_id, total_earnings, available_balance) 
        VALUES (?, ?, 0, 0)
        ON DUPLICATE KEY UPDATE shop_id = shop_id
    ");
    $stmt->execute([$_SESSION['shop_id'], $_SESSION['shop_owner_id']]);
    
    $balance = [
        'total_earnings' => 0,
        'withdrawn_amount' => 0,
        'pending_withdrawal' => 0,
        'available_balance' => 0
    ];
}

// Get payment methods
$paymentMethods = $paymentWithdrawalManager->getPaymentMethodSettings();

// Handle withdrawal request submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_withdrawal') {
    try {
        $requestData = [
            'amount' => floatval($_POST['amount']),
            'payment_method' => sanitize($_POST['payment_method']),
            'account_type' => sanitize($_POST['account_type']),
            'account_number' => sanitize($_POST['account_number']),
            'account_holder_name' => sanitize($_POST['account_holder_name']),
            'notes' => sanitize($_POST['notes'] ?? '')
        ];
        
        $requestId = $paymentWithdrawalManager->createWithdrawalRequest(
            $_SESSION['shop_id'], 
            $_SESSION['shop_owner_id'], 
            $requestData
        );
        
        $success = "Withdrawal request submitted successfully! Request ID: #$requestId";
        
        // Refresh balance
        $balance = $paymentWithdrawalManager->getShopOwnerBalance($_SESSION['shop_id'], $_SESSION['shop_owner_id']);
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get recent withdrawal requests
$recentRequests = $paymentWithdrawalManager->getWithdrawalRequests(
    ['shop_id' => $_SESSION['shop_id']], 
    10, 
    0
);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Withdrawal Request</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Balance Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="card-title h5">Total Earnings</div>
                            <div class="h3">৳<?= number_format($balance['total_earnings'], 2) ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="card-title h5">Available Balance</div>
                            <div class="h3">৳<?= number_format($balance['available_balance'], 2) ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="card-title h5">Pending Withdrawal</div>
                            <div class="h3">৳<?= number_format($balance['pending_withdrawal'], 2) ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="card-title h5">Total Withdrawn</div>
                            <div class="h3">৳<?= number_format($balance['withdrawn_amount'], 2) ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Withdrawal Request Form -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>New Withdrawal Request
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($balance['available_balance'] <= 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            You don't have sufficient balance to make a withdrawal request.
                        </div>
                    <?php else: ?>
                        <form method="POST" id="withdrawalForm">
                            <input type="hidden" name="action" value="submit_withdrawal">
                            
                            <div class="mb-3">
                                <label for="amount" class="form-label">Withdrawal Amount *</label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    <input type="number" class="form-control" id="amount" name="amount" 
                                           min="50" max="<?= $balance['available_balance'] ?>" step="0.01" required>
                                </div>
                                <div class="form-text">
                                    Available: ৳<?= number_format($balance['available_balance'], 2) ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method *</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">Select Payment Method</option>
                                    <?php foreach ($paymentMethods as $method): ?>
                                        <option value="<?= $method['method_name'] ?>" 
                                                data-min="<?= $method['min_amount'] ?>"
                                                data-max="<?= $method['max_amount'] ?>"
                                                data-fee-type="<?= $method['processing_fee_type'] ?>"
                                                data-fee-value="<?= $method['processing_fee_value'] ?>"
                                                data-pattern="<?= htmlspecialchars($method['account_number_pattern'] ?? '') ?>"
                                                data-example="<?= htmlspecialchars($method['account_number_example'] ?? '') ?>">
                                            <?= htmlspecialchars($method['display_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text" id="method_info"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_type" class="form-label">Account Type *</label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">Select Account Type</option>
                                    <option value="personal">Personal Account</option>
                                    <option value="agent">Agent Account</option>
                                    <option value="merchant">Merchant Account</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_number" class="form-label">Account Number *</label>
                                <input type="text" class="form-control" id="account_number" name="account_number" required>
                                <div class="form-text" id="account_example"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_holder_name" class="form-label">Account Holder Name *</label>
                                <input type="text" class="form-control" id="account_holder_name" name="account_holder_name" required>
                                <div class="form-text">Enter the name as registered with the payment method</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="Any additional information..."></textarea>
                            </div>
                            
                            <div class="mb-3" id="fee_calculation" style="display: none;">
                                <div class="alert alert-info">
                                    <strong>Fee Calculation:</strong><br>
                                    Withdrawal Amount: ৳<span id="calc_amount">0</span><br>
                                    Processing Fee: ৳<span id="calc_fee">0</span><br>
                                    <strong>You will receive: ৳<span id="calc_final">0</span></strong>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane"></i> Submit Withdrawal Request
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Requests -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Withdrawal Requests
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recentRequests)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No withdrawal requests found.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentRequests as $request): ?>
                                        <tr>
                                            <td>#<?= $request['id'] ?></td>
                                            <td>৳<?= number_format($request['request_amount'], 2) ?></td>
                                            <td><?= htmlspecialchars($request['payment_method_name'] ?? $request['payment_method']) ?></td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'processing' => 'info',
                                                    'approved' => 'primary',
                                                    'completed' => 'success',
                                                    'rejected' => 'danger',
                                                    'cancelled' => 'secondary'
                                                ][$request['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $statusClass ?>">
                                                    <?= ucfirst($request['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($request['created_at'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('payment_method');
    const amountInput = document.getElementById('amount');
    const accountNumberInput = document.getElementById('account_number');
    
    // Update method info and validation when payment method changes
    paymentMethodSelect.addEventListener('change', function() {
        const option = this.selectedOptions[0];
        const methodInfo = document.getElementById('method_info');
        const accountExample = document.getElementById('account_example');
        
        if (option.value) {
            const minAmount = parseFloat(option.dataset.min);
            const maxAmount = parseFloat(option.dataset.max);
            const example = option.dataset.example;
            
            methodInfo.innerHTML = `Min: ৳${minAmount.toLocaleString()}, Max: ৳${maxAmount.toLocaleString()}`;
            accountExample.innerHTML = example ? `Example: ${example}` : '';
            
            // Update amount input constraints
            amountInput.min = minAmount;
            amountInput.max = Math.min(maxAmount, <?= $balance['available_balance'] ?>);
            
            // Update account number pattern
            if (option.dataset.pattern) {
                accountNumberInput.pattern = option.dataset.pattern;
            } else {
                accountNumberInput.removeAttribute('pattern');
            }
        } else {
            methodInfo.innerHTML = '';
            accountExample.innerHTML = '';
        }
        
        calculateFee();
    });
    
    // Calculate fee when amount changes
    amountInput.addEventListener('input', calculateFee);
    
    function calculateFee() {
        const amount = parseFloat(amountInput.value) || 0;
        const option = paymentMethodSelect.selectedOptions[0];
        const feeCalc = document.getElementById('fee_calculation');
        
        if (amount > 0 && option && option.value) {
            const feeType = option.dataset.feeType;
            const feeValue = parseFloat(option.dataset.feeValue);
            
            let fee = 0;
            if (feeType === 'percentage') {
                fee = (amount * feeValue) / 100;
            } else {
                fee = feeValue;
            }
            
            const finalAmount = amount - fee;
            
            document.getElementById('calc_amount').textContent = amount.toLocaleString();
            document.getElementById('calc_fee').textContent = fee.toLocaleString();
            document.getElementById('calc_final').textContent = finalAmount.toLocaleString();
            
            feeCalc.style.display = 'block';
        } else {
            feeCalc.style.display = 'none';
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
