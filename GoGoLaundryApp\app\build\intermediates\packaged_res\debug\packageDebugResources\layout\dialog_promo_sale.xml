<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:clickable="true"
    android:focusable="true">

    <!-- Main Dialog Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="392dp"
        android:layout_height="732dp"
        android:layout_gravity="center"
        android:layout_marginHorizontal="56dp"
        android:layout_marginVertical="80dp"
        android:background="#80000000">

        <!-- Close Button (positioned above dialog) -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="15dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="32dp"
            android:background="@drawable/promo_close_button_background"
            android:contentDescription="@string/close"
            android:elevation="24dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/dialog_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            app:tint="@color/white" />


        <!-- Dialog Content -->
        <androidx.cardview.widget.CardView
            android:id="@+id/dialog_content"
            android:layout_width="0dp"
            android:layout_height="600dp"
            android:layout_marginTop="1dp"
            android:layout_marginBottom="1dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_max="280dp">


            <!-- Glass Background -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/promo_dialog_background" />

            <!-- ScrollView for Content -->
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true"
                android:scrollbars="none">

                <!-- Content Layout -->
                <LinearLayout
                    android:id="@+id/main_content_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:orientation="vertical"
                    android:padding="16dp">

                <!-- Header Section -->
                <LinearLayout
                    android:id="@+id/header_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:gravity="center"
                    android:orientation="vertical">
                    <!-- Main Title -->
                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center"
                        android:text="HOT PICKS\nLOW PRICES"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- Subtitle -->
                    <TextView
                        android:id="@+id/tv_subtitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center"
                        android:text="Best Deals on Best Prices"
                        android:textColor="#FFD700"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <!-- Dynamic Promo Image -->
                    <ImageView
                        android:id="@+id/iv_promo_image"
                        android:layout_width="match_parent"
                        android:layout_height="320dp"
                        android:layout_marginTop="12dp"
                        android:contentDescription="Promotional Image"
                        android:scaleType="fitXY"
                        android:visibility="gone" />

                </LinearLayout>

                <!-- Products Section -->
                <LinearLayout
                    android:id="@+id/products_layout"
                    android:background="@android:color/transparent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <!-- Discount Badge -->
                    <LinearLayout
                        android:id="@+id/discount_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_discount_text"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/circle_background"
                            android:backgroundTint="#FFD700"
                            android:gravity="center"
                            android:text="UP TO\n60%\nOFF"
                            android:textColor="@color/primary"
                            android:textSize="8sp"
                            android:textStyle="bold" />

                        <!-- Promo Code -->
                        <TextView
                            android:id="@+id/tv_promo_code"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:background="@drawable/glass_section_background"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="2dp"
                            android:text="SAVE60"
                            android:textColor="@color/white"
                            android:textSize="8sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Shop Now Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_shop_now"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="16dp"
                    android:layout_gravity="bottom"
                    android:background="@drawable/button_accent"
                    android:text="Shop Now"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:backgroundTint="#FFD700"
                    app:cornerRadius="22dp" />

                </LinearLayout>

            </ScrollView>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
