<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Order Details';
$currentPage = 'orders';

// Get order ID
$orderId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$orderId) {
    $_SESSION['shop_error_message'] = 'Invalid order ID.';
    header('Location: orders.php');
    exit;
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $newStatus = sanitize($_POST['new_status']);
    
    try {
        $updateStmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ? AND shop_id = ?");
        if ($updateStmt->execute([$newStatus, $orderId, $shopOwnerData['shop_id']])) {
            $_SESSION['shop_success_message'] = 'Order status updated successfully!';
        } else {
            $_SESSION['shop_error_message'] = 'Failed to update order status.';
        }
    } catch (PDOException $e) {
        error_log('Order status update error: ' . $e->getMessage());
        $_SESSION['shop_error_message'] = 'Failed to update order status.';
    }
}

// Get order details
try {
    $orderStmt = $pdo->prepare("
        SELECT o.*, u.full_name as customer_name, u.phone as customer_phone, 
               u.email as customer_email, u.address as customer_address
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.id = ? AND o.shop_id = ?
    ");
    $orderStmt->execute([$orderId, $shopOwnerData['shop_id']]);
    $order = $orderStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        $_SESSION['shop_error_message'] = 'Order not found.';
        header('Location: orders.php');
        exit;
    }
    
    // Get order items
    $itemsStmt = $pdo->prepare("
        SELECT oi.*, i.name as item_name, i.bn_name as item_bn_name, 
               i.image_url, s.name as service_name
        FROM order_items oi
        JOIN items i ON oi.item_id = i.id
        JOIN services s ON i.service_id = s.id
        WHERE oi.order_id = ?
        ORDER BY s.name, i.name
    ");
    $itemsStmt->execute([$orderId]);
    $orderItems = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    error_log('Order details fetch error: ' . $e->getMessage());
    $_SESSION['shop_error_message'] = 'Failed to load order details.';
    header('Location: orders.php');
    exit;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Order Details #<?= $order['id'] ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="orders.php" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>Back to Orders
        </a>
        <?php if ($order['status'] !== 'delivered' && $order['status'] !== 'cancelled'): ?>
            <button type="button" class="btn btn-primary me-2" 
                    onclick="updateOrderStatus(<?= $order['id'] ?>, '<?= $order['status'] ?>')">
                <i class="fas fa-edit me-1"></i>Update Status
            </button>
        <?php endif; ?>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="fas fa-print me-1"></i>Print
        </button>
    </div>
</div>

<div class="row">
    <!-- Order Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Order Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <strong>Order ID:</strong> #<?= $order['id'] ?>
                        </div>
                        <div class="info-item">
                            <strong>Status:</strong>
                            <?php
                            $statusClass = '';
                            $statusText = '';
                            switch ($order['status']) {
                                case 'placed': 
                                    $statusClass = 'bg-warning text-dark'; 
                                    $statusText = 'New Order';
                                    break;
                                case 'processing': 
                                    $statusClass = 'bg-info'; 
                                    $statusText = 'Processing';
                                    break;
                                case 'out_for_delivery': 
                                    $statusClass = 'bg-primary'; 
                                    $statusText = 'Out for Delivery';
                                    break;
                                case 'delivered': 
                                    $statusClass = 'bg-success'; 
                                    $statusText = 'Completed';
                                    break;
                                case 'cancelled': 
                                    $statusClass = 'bg-danger'; 
                                    $statusText = 'Cancelled';
                                    break;
                                default: 
                                    $statusClass = 'bg-secondary';
                                    $statusText = ucfirst($order['status']);
                            }
                            ?>
                            <span class="badge <?= $statusClass ?> fs-6"><?= $statusText ?></span>
                        </div>
                        <div class="info-item">
                            <strong>Order Date:</strong> <?= date('M j, Y g:i A', strtotime($order['created_at'])) ?>
                        </div>
                        <div class="info-item">
                            <strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($order['updated_at'])) ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <strong>Total Amount:</strong> <span class="text-primary fs-5">৳<?= number_format($order['total'], 2) ?></span>
                        </div>
                        <div class="info-item">
                            <strong>Payment Method:</strong> <?= ucfirst($order['payment_method'] ?? 'Cash on Delivery') ?>
                        </div>
                        <div class="info-item">
                            <strong>Delivery Type:</strong> <?= ucfirst($order['delivery_type'] ?? 'Standard') ?>
                        </div>
                        <?php if ($order['special_instructions']): ?>
                            <div class="info-item">
                                <strong>Special Instructions:</strong><br>
                                <em><?= htmlspecialchars($order['special_instructions']) ?></em>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Items -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Order Items (<?= count($orderItems) ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Service</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orderItems as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($item['image_url']): ?>
                                                <img src="<?= htmlspecialchars($item['image_url']) ?>" 
                                                     class="item-image me-2" alt="Item">
                                            <?php endif; ?>
                                            <div>
                                                <strong><?= htmlspecialchars($item['item_name']) ?></strong>
                                                <?php if ($item['item_bn_name']): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($item['item_bn_name']) ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($item['service_name']) ?></td>
                                    <td><?= $item['quantity'] ?></td>
                                    <td>৳<?= number_format($item['price'], 2) ?></td>
                                    <td><strong>৳<?= number_format($item['price'] * $item['quantity'], 2) ?></strong></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-active">
                                <th colspan="4" class="text-end">Total Amount:</th>
                                <th>৳<?= number_format($order['total'], 2) ?></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Customer Information -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Customer Information
                </h5>
            </div>
            <div class="card-body">
                <div class="customer-info">
                    <div class="info-item">
                        <strong>Name:</strong><br>
                        <?= htmlspecialchars($order['customer_name'] ?? 'N/A') ?>
                    </div>
                    <div class="info-item">
                        <strong>Phone:</strong><br>
                        <?php if ($order['customer_phone']): ?>
                            <a href="tel:<?= $order['customer_phone'] ?>" class="text-decoration-none">
                                <?= htmlspecialchars($order['customer_phone']) ?>
                            </a>
                        <?php else: ?>
                            N/A
                        <?php endif; ?>
                    </div>
                    <div class="info-item">
                        <strong>Email:</strong><br>
                        <?php if ($order['customer_email']): ?>
                            <a href="mailto:<?= $order['customer_email'] ?>" class="text-decoration-none">
                                <?= htmlspecialchars($order['customer_email']) ?>
                            </a>
                        <?php else: ?>
                            N/A
                        <?php endif; ?>
                    </div>
                    <div class="info-item">
                        <strong>Address:</strong><br>
                        <?= htmlspecialchars($order['customer_address'] ?? $order['delivery_address'] ?? 'N/A') ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Timeline -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Order Timeline
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item <?= in_array($order['status'], ['placed', 'processing', 'out_for_delivery', 'delivered']) ? 'completed' : '' ?>">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>Order Placed</h6>
                            <small class="text-muted"><?= date('M j, Y g:i A', strtotime($order['created_at'])) ?></small>
                        </div>
                    </div>
                    
                    <div class="timeline-item <?= in_array($order['status'], ['processing', 'out_for_delivery', 'delivered']) ? 'completed' : '' ?>">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>Processing</h6>
                            <small class="text-muted">
                                <?= $order['status'] === 'processing' || in_array($order['status'], ['out_for_delivery', 'delivered']) ? 
                                    date('M j, Y g:i A', strtotime($order['updated_at'])) : 'Pending' ?>
                            </small>
                        </div>
                    </div>
                    
                    <div class="timeline-item <?= in_array($order['status'], ['out_for_delivery', 'delivered']) ? 'completed' : '' ?>">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>Out for Delivery</h6>
                            <small class="text-muted">
                                <?= $order['status'] === 'out_for_delivery' || $order['status'] === 'delivered' ? 
                                    date('M j, Y g:i A', strtotime($order['updated_at'])) : 'Pending' ?>
                            </small>
                        </div>
                    </div>
                    
                    <div class="timeline-item <?= $order['status'] === 'delivered' ? 'completed' : '' ?>">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>Delivered</h6>
                            <small class="text-muted">
                                <?= $order['status'] === 'delivered' ? 
                                    date('M j, Y g:i A', strtotime($order['updated_at'])) : 'Pending' ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    
                    <div class="alert alert-info">
                        <strong>Order #<?= $order['id'] ?></strong><br>
                        Customer: <?= htmlspecialchars($order['customer_name'] ?? 'N/A') ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status</label>
                        <select class="form-select" id="new_status" name="new_status" required>
                            <option value="placed" <?= $order['status'] === 'placed' ? 'selected' : '' ?>>New Order</option>
                            <option value="processing" <?= $order['status'] === 'processing' ? 'selected' : '' ?>>Processing</option>
                            <option value="out_for_delivery" <?= $order['status'] === 'out_for_delivery' ? 'selected' : '' ?>>Out for Delivery</option>
                            <option value="delivered" <?= $order['status'] === 'delivered' ? 'selected' : '' ?>>Completed</option>
                            <option value="cancelled" <?= $order['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.info-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3e6f0;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.item-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e3e6f0;
    border: 2px solid #fff;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-item.completed .timeline-content h6 {
    color: #28a745;
}

@media print {
    .btn-toolbar, .modal, .sidebar, .navbar { display: none !important; }
    .content { margin-left: 0 !important; }
}
</style>

<script>
function updateOrderStatus(orderId, currentStatus) {
    document.getElementById('new_status').value = currentStatus;
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
