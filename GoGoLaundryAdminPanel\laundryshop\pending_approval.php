<?php
/**
 * Pending Approval Page
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ShopOwnerManager.php';

// Check if shop owner is logged in
if (!isset($_SESSION['shop_owner_id']) || !isset($_SESSION['shop_owner_logged_in']) || $_SESSION['shop_owner_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Initialize shop owner manager
$shopOwnerManager = new ShopOwnerManager($pdo);

// Get shop owner data
$shopOwner = $shopOwnerManager->getShopOwnerById($_SESSION['shop_owner_id']);

// If shop is verified, redirect to dashboard
if ($shopOwner && $shopOwner['shop_is_verified'] && $shopOwner['shop_is_active']) {
    header('Location: index.php');
    exit;
}

$pageTitle = 'Pending Approval';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .pending-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .pending-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            padding: 2rem;
        }
        
        .pending-body {
            padding: 2rem;
        }
        
        .status-icon {
            font-size: 4rem;
            color: #ffc107;
            margin-bottom: 1rem;
        }
        
        .btn-refresh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 24px;
            font-weight: 500;
        }
        
        .shop-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-verified {
            background: #d1edff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="pending-container">
        <div class="pending-header">
            <i class="fas fa-clock fa-3x mb-3"></i>
            <h2>Pending Approval</h2>
            <div class="subtitle">Your shop is under review</div>
        </div>
        
        <div class="pending-body">
            <div class="status-icon">
                <i class="fas fa-hourglass-half"></i>
            </div>
            
            <h4 class="mb-3">Thank you for registering!</h4>
            
            <p class="text-muted mb-4">
                Your shop registration has been submitted successfully. Our admin team is currently reviewing your application. 
                You will be notified once your shop has been approved and verified.
            </p>
            
            <div class="shop-info">
                <h6 class="mb-3"><i class="fas fa-store me-2"></i>Shop Information</h6>
                
                <div class="info-item">
                    <span><strong>Shop Name:</strong></span>
                    <span><?php echo htmlspecialchars($shopOwner['shop_name'] ?? 'N/A'); ?></span>
                </div>
                
                <div class="info-item">
                    <span><strong>Owner:</strong></span>
                    <span><?php echo htmlspecialchars($shopOwner['full_name'] ?? 'N/A'); ?></span>
                </div>
                
                <div class="info-item">
                    <span><strong>Phone:</strong></span>
                    <span><?php echo htmlspecialchars($shopOwner['phone'] ?? 'N/A'); ?></span>
                </div>
                
                <div class="info-item">
                    <span><strong>Status:</strong></span>
                    <span>
                        <?php if ($shopOwner['shop_is_verified']): ?>
                            <span class="status-badge status-verified">
                                <i class="fas fa-check-circle me-1"></i>Verified
                            </span>
                        <?php else: ?>
                            <span class="status-badge status-pending">
                                <i class="fas fa-clock me-1"></i>Pending Review
                            </span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <strong>What happens next?</strong><br>
                <small>
                    1. Admin reviews your shop details<br>
                    2. Shop verification and approval<br>
                    3. Email notification sent to you<br>
                    4. Access to shop owner dashboard
                </small>
            </div>
            
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary btn-refresh" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-2"></i>Refresh Status
                </button>
                
                <a href="logout.php" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
            
            <div class="mt-4 pt-3 border-top">
                <small class="text-muted">
                    <i class="fas fa-phone me-1"></i>
                    Need help? Contact support at: 
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-refresh every 30 seconds to check for approval
        setInterval(function() {
            location.reload();
        }, 30000);
        
        // Show loading state when refreshing
        document.querySelector('.btn-refresh').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
            this.disabled = true;
        });
    </script>
</body>
</html>
