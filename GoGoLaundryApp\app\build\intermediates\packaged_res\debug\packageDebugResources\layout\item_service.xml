<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/margin_small"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/elevation_card">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_medium">

        <ImageView
            android:id="@+id/service_icon"
            android:layout_width="@dimen/icon_size_large"
            android:layout_height="@dimen/icon_size_large"
            android:contentDescription="@string/service_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/ic_washing_machine" />

        <TextView
            android:id="@+id/service_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/service_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/service_price"
            tools:text="Wash &amp; Fold" />

        <TextView
            android:id="@+id/service_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_extra_small"
            android:textSize="@dimen/text_size_small"
            android:textColor="@color/text_secondary"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/service_icon"
            app:layout_constraintTop_toBottomOf="@id/service_name"
            app:layout_constraintEnd_toStartOf="@id/service_price"
            tools:text="Regular washing and folding service for all types of clothes" />

        <TextView
            android:id="@+id/service_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/primary"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="৳120/kg" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_add_to_cart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/add_to_cart"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/service_description"
            app:layout_constraintBottom_toBottomOf="parent"
            style="@style/Widget.MaterialComponents.Button.TextButton" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
