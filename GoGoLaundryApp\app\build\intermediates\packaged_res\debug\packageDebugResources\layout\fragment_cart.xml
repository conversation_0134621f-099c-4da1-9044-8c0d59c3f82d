﻿<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="6dp">

        <!-- Compact Cart Header Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cart_header_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="3dp"
            android:background="@drawable/glass_hero_background"
            android:elevation="8dp"
            android:paddingStart="16dp"
            android:paddingTop="12dp"
            android:paddingEnd="16dp"
            android:paddingBottom="12dp"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Compact Cart Icon and Title -->
            <FrameLayout
                android:id="@+id/cart_icon_container"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="@drawable/glass_icon_background"
                android:elevation="4dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_cart"
                    app:tint="@color/home_accent_blue" />

            </FrameLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@id/cart_icon_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/cart_icon_container"
                app:layout_constraintTop_toTopOf="@id/cart_icon_container">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Shopping Cart"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    android:textColor="@color/home_text_on_gradient"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/cart_item_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:text="0 items in cart"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                    android:textColor="@color/home_text_on_gradient"
                    android:alpha="0.9" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Cart Content Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cart_content_section"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="6dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="3dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="12dp"
            android:paddingTop="12dp"
            android:paddingEnd="12dp"
            android:paddingBottom="12dp"
            app:layout_constraintBottom_toTopOf="@id/cart_summary_section"
            app:layout_constraintTop_toBottomOf="@id/cart_header_section">

            <!-- Cart Items Container -->
            <FrameLayout
                android:id="@+id/cart_items_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="8dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/cart_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="true"
                    android:overScrollMode="never"
                    android:padding="2dp"
                    android:scrollbars="vertical"
                    android:fadeScrollbars="true"
                    android:scrollbarStyle="outsideOverlay"
                    tools:listitem="@layout/item_cart" />

                <!-- Beautiful Empty State -->
                <LinearLayout
                    android:id="@+id/empty_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <FrameLayout
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/glass_icon_background"
                        android:elevation="4dp">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_cart"
                            app:tint="@color/home_accent_blue"
                            android:alpha="0.7" />

                    </FrameLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Your cart is empty"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Add some items to get started"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                        android:textColor="@color/text_secondary"
                        android:alpha="0.8" />

                </LinearLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Compact Cart Summary Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cart_summary_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="10dp"
            android:paddingTop="8dp"
            android:paddingEnd="10dp"
            android:paddingBottom="8dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- Compact Total Display -->
            <LinearLayout
                android:id="@+id/total_display_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/glass_section_background"
                android:elevation="2dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="12dp"
                android:paddingTop="8dp"
                android:paddingEnd="12dp"
                android:paddingBottom="8dp"
                app:layout_constraintTop_toTopOf="parent">

                <!-- Total Icon and Label -->
                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="@drawable/glass_icon_background"
                    android:elevation="2dp">

                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_receipt"
                        app:tint="@color/home_accent_blue" />

                </FrameLayout>

                <!-- Total Amount Info -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Amount"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="@color/text_secondary"
                        android:textStyle="bold"
                        android:textAllCaps="true"
                        android:letterSpacing="0.1" />

                    <TextView
                        android:id="@+id/cart_total"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="৳0.00"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/home_accent_blue"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Quick Total Badge -->
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/glass_button_background"
                    android:elevation="2dp"
                    android:paddingStart="12dp"
                    android:paddingTop="6dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="6dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="TOTAL"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="@color/home_accent_blue"
                        android:textStyle="bold"
                        android:textAllCaps="true"
                        android:letterSpacing="0.15" />

                </FrameLayout>

            </LinearLayout>

            <!-- Ultra Compact Action Buttons -->
            <LinearLayout
                android:id="@+id/action_buttons_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/total_display_container">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/checkout_button"
                    android:layout_width="0dp"
                    android:layout_height="42dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    android:text="Checkout"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    android:textSize="13sp"
                    android:backgroundTint="@color/home_accent_blue"
                    android:elevation="4dp"
                    app:cornerRadius="14dp"
                    app:icon="@drawable/ic_arrow_forward"
                    app:iconTint="@android:color/white"
                    app:iconGravity="textEnd"
                    app:iconSize="14dp"
                    app:iconPadding="4dp"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/clear_cart_button"
                    android:layout_width="0dp"
                    android:layout_height="42dp"
                    android:layout_weight="0.6"
                    android:layout_marginStart="6dp"
                    android:text="Clear"
                    android:textColor="@color/error"
                    android:textStyle="bold"
                    android:textSize="13sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/error"
                    app:strokeWidth="1.5dp"
                    app:cornerRadius="14dp"
                    app:icon="@drawable/ic_delete"
                    app:iconTint="@color/error"
                    app:iconGravity="textStart"
                    app:iconSize="14dp"
                    app:iconPadding="3dp" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
