<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="6dp"
        android:paddingTop="6dp"
        android:paddingEnd="6dp"
        android:paddingBottom="16dp">

        <!-- Header Section with Glass Effect -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/notification_header_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_hero_background"
            android:elevation="8dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Header Title with Back Button -->
            <LinearLayout
                android:id="@+id/notification_title_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <!-- Back Button -->
                <FrameLayout
                    android:id="@+id/back_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/glass_button_background"
                    android:elevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackgroundBorderless">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_arrow_back"
                        app:tint="@color/home_accent_blue" />

                </FrameLayout>

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_notification"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Notification Details"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/home_text_on_gradient"
                    android:textStyle="bold"
                    android:letterSpacing="0.02" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Notification Content Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/notification_content_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/notification_header_section">

            <!-- Notification Details Container -->
            <FrameLayout
                android:id="@+id/notification_details_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="24dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- Notification Icon with Glass Background -->
                    <FrameLayout
                        android:id="@+id/notification_icon_container"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:background="@drawable/glass_icon_background"
                        android:elevation="4dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/notification_icon"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_notification"
                            app:tint="@color/home_accent_blue" />

                    </FrameLayout>

                    <!-- Notification Type and Timestamp -->
                    <LinearLayout
                        android:id="@+id/notification_meta_container"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/notification_icon_container"
                        app:layout_constraintTop_toTopOf="@id/notification_icon_container">

                        <TextView
                            android:id="@+id/notification_type"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/glass_button_background"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="6dp"
                            android:text="Order Update"
                            android:textColor="@color/home_accent_blue"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textAllCaps="true"
                            android:letterSpacing="0.1"
                            tools:text="PROMOTION" />

                        <TextView
                            android:id="@+id/notification_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:text="2 hours ago"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:alpha="0.8"
                            tools:text="2 hours ago" />

                    </LinearLayout>

                    <!-- Title -->
                    <TextView
                        android:id="@+id/notification_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="Your order has been confirmed"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold"
                        android:lineSpacingExtra="2dp"
                        app:layout_constraintTop_toBottomOf="@id/notification_icon_container"
                        tools:text="Testing notification" />

                    <!-- Message -->
                    <TextView
                        android:id="@+id/notification_message"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:lineSpacingExtra="6dp"
                        android:text="Great news! Your laundry order has been confirmed and is being processed."
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary"
                        android:alpha="0.9"
                        app:layout_constraintTop_toBottomOf="@id/notification_title"
                        tools:text="This is a test notification with an image to verify the Android app displays images correctly." />

                    <!-- Notification Image Container (if available) -->
                    <FrameLayout
                        android:id="@+id/notification_image_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/glass_section_background"
                        android:elevation="2dp"
                        android:padding="8dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/notification_message"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/notification_image"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:background="@drawable/rounded_corner_background"
                            android:scaleType="centerCrop"
                            android:contentDescription="@string/notification_image"
                            tools:src="@drawable/placeholder_image" />

                    </FrameLayout>

                    <!-- Order Information Container (if applicable) -->
                    <FrameLayout
                        android:id="@+id/order_info_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/glass_section_background"
                        android:elevation="2dp"
                        android:padding="20dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/notification_image_container"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/order_info_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_marginEnd="8dp"
                                    android:src="@drawable/ic_receipt"
                                    app:tint="@color/home_accent_blue" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Order Information"
                                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                                    android:textColor="@color/home_accent_blue"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/order_number"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Order #12345"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                                android:textColor="@color/text_primary"
                                tools:text="Order #12345" />

                        </LinearLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Action Buttons Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/notification_actions_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/notification_content_section">

            <!-- Action Buttons Container -->
            <FrameLayout
                android:id="@+id/action_buttons_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="20dp"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_mark_read"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="Mark as Read"
                        android:textColor="@android:color/white"
                        android:textStyle="bold"
                        android:backgroundTint="@color/home_accent_blue"
                        android:elevation="6dp"
                        app:cornerRadius="20dp"
                        app:icon="@drawable/ic_check"
                        app:iconTint="@android:color/white"
                        app:iconGravity="textStart"
                        app:iconPadding="8dp"
                        style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_view_order"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="View Order"
                        android:textColor="@color/home_accent_blue"
                        android:textStyle="bold"
                        android:visibility="gone"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        app:strokeColor="@color/home_accent_blue"
                        app:strokeWidth="2dp"
                        app:cornerRadius="20dp"
                        app:icon="@drawable/ic_receipt"
                        app:iconTint="@color/home_accent_blue"
                        app:iconGravity="textStart"
                        app:iconPadding="8dp"
                        tools:visibility="visible" />

                </LinearLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
