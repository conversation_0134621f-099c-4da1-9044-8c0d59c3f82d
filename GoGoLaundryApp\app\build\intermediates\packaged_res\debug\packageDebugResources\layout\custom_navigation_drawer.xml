<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <!-- Main content -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Include custom toolbar -->
            <include
                android:id="@+id/custom_toolbar"
                layout="@layout/custom_toolbar" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Main content container -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <!-- Fragment container above bottom navigation -->
            <FrameLayout
                android:id="@+id/fragment_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/bottom_navigation_layout" />

            <!-- Custom Bottom Navigation -->
            <include
                android:id="@+id/bottom_navigation_layout"
                layout="@layout/custom_bottom_navigation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Enhanced Navigation drawer with material design -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        android:background="@android:color/white"
        app:itemIconTint="@color/nav_item_color_state"
        app:itemTextColor="@color/nav_item_color_state"
        app:itemIconPadding="16dp"
        app:itemHorizontalPadding="24dp"
        app:itemShapeFillColor="@color/nav_item_background_color"
        app:itemShapeAppearance="@style/NavigationDrawerItemShape"
        app:headerLayout="@layout/nav_header_enhanced"
        app:menu="@menu/drawer_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
