<?php
/**
 * Enhanced Admin Shop Management Dashboard
 *
 * Features:
 * - Comprehensive shop management with analytics
 * - Commission management and tracking
 * - Order status monitoring
 * - Performance analytics
 * - Modern responsive UI
 */

// Include authentication middleware
require_once 'auth.php';

// Include required managers
require_once '../includes/CommissionManager.php';
require_once '../includes/ShopOwnerManager.php';
require_once '../includes/PaymentWithdrawalManager.php';

$pageTitle = 'Shop Management Dashboard';
$currentPage = 'shops';

// Initialize managers
$commissionManager = new CommissionManager($pdo);
$shopOwnerManager = new ShopOwnerManager($pdo);
$paymentWithdrawalManager = new PaymentWithdrawalManager($pdo);

// Handle AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');

    switch ($_GET['ajax']) {
        case 'shop_stats':
            $shopId = intval($_GET['shop_id']);
            $stats = getShopDetailedStats($pdo, $shopId);
            echo json_encode($stats);
            exit;

        case 'commission_history':
            $shopId = intval($_GET['shop_id']);
            $history = $commissionManager->getShopCommissionHistory($shopId, 10);
            echo json_encode($history);
            exit;

        case 'dashboard_stats':
            $stats = getDashboardStats($pdo);
            echo json_encode($stats);
            exit;

        case 'withdrawal_requests':
            $page = intval($_GET['page'] ?? 1);
            $limit = intval($_GET['limit'] ?? 10);
            $offset = ($page - 1) * $limit;

            $filters = [
                'status' => $_GET['status'] ?? '',
                'shop_id' => $_GET['shop_id'] ?? '',
                'payment_method' => $_GET['payment_method'] ?? '',
                'date_from' => $_GET['date_from'] ?? '',
                'date_to' => $_GET['date_to'] ?? ''
            ];

            $requests = $paymentWithdrawalManager->getWithdrawalRequests($filters, $limit, $offset);
            $total = $paymentWithdrawalManager->getWithdrawalRequestsCount($filters);

            echo json_encode([
                'requests' => $requests,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit)
            ]);
            exit;

        case 'withdrawal_stats':
            $shopId = $_GET['shop_id'] ?? null;
            $stats = $paymentWithdrawalManager->getWithdrawalStatistics($shopId);
            echo json_encode($stats);
            exit;

        case 'payment_methods':
            $methods = $paymentWithdrawalManager->getPaymentMethodSettings();
            echo json_encode($methods);
            exit;
    }
}

// Handle shop actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];

    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'verify_shop':
                    $shopId = intval($_POST['shop_id']);
                    $notes = sanitize($_POST['admin_notes'] ?? '');

                    $stmt = $pdo->prepare("UPDATE laundry_shops SET is_verified = 1, admin_notes = ?, verified_at = NOW() WHERE id = ?");
                    if ($stmt->execute([$notes, $shopId])) {
                        logAdminAction($pdo, $_SESSION['admin_id'], 'shop_verified', "Shop ID: $shopId", $shopId);
                        $response = ['success' => true, 'message' => 'Shop verified successfully!'];
                    } else {
                        $response['message'] = 'Failed to verify shop.';
                    }
                    break;

                case 'activate_shop':
                    $shopId = intval($_POST['shop_id']);
                    $stmt = $pdo->prepare("UPDATE laundry_shops SET is_active = 1 WHERE id = ?");
                    if ($stmt->execute([$shopId])) {
                        logAdminAction($pdo, $_SESSION['admin_id'], 'shop_activated', "Shop ID: $shopId", $shopId);
                        $response = ['success' => true, 'message' => 'Shop activated successfully!'];
                    } else {
                        $response['message'] = 'Failed to activate shop.';
                    }
                    break;

                case 'deactivate_shop':
                    $shopId = intval($_POST['shop_id']);
                    $reason = sanitize($_POST['deactivation_reason'] ?? '');

                    $stmt = $pdo->prepare("UPDATE laundry_shops SET is_active = 0, admin_notes = ? WHERE id = ?");
                    if ($stmt->execute([$reason, $shopId])) {
                        logAdminAction($pdo, $_SESSION['admin_id'], 'shop_deactivated', "Shop ID: $shopId, Reason: $reason", $shopId);
                        $response = ['success' => true, 'message' => 'Shop deactivated successfully!'];
                    } else {
                        $response['message'] = 'Failed to deactivate shop.';
                    }
                    break;

                case 'update_commission':
                    $shopId = intval($_POST['shop_id']);
                    $commission = floatval($_POST['commission_percentage']);
                    $effectiveDate = $_POST['effective_date'] ?? date('Y-m-d');

                    if ($commission < 0 || $commission > 50) {
                        $response['message'] = 'Commission rate must be between 0% and 50%.';
                        break;
                    }

                    $stmt = $pdo->prepare("UPDATE laundry_shops SET commission_percentage = ?, commission_updated_at = NOW() WHERE id = ?");
                    if ($stmt->execute([$commission, $shopId])) {
                        // Log commission change
                        $logStmt = $pdo->prepare("
                            INSERT INTO commission_logs (shop_id, old_rate, new_rate, changed_by, effective_date, created_at)
                            VALUES (?, (SELECT commission_percentage FROM laundry_shops WHERE id = ?), ?, ?, ?, NOW())
                        ");
                        $logStmt->execute([$shopId, $shopId, $commission, $_SESSION['admin_id'], $effectiveDate]);

                        logAdminAction($pdo, $_SESSION['admin_id'], 'commission_updated', "Shop ID: $shopId, New Rate: $commission%", $shopId);
                        $response = ['success' => true, 'message' => 'Commission rate updated successfully!'];
                    } else {
                        $response['message'] = 'Failed to update commission rate.';
                    }
                    break;

                case 'bulk_update_commission':
                    $shopIds = $_POST['shop_ids'] ?? [];
                    $commission = floatval($_POST['bulk_commission_percentage']);
                    $effectiveDate = $_POST['bulk_effective_date'] ?? date('Y-m-d');

                    if (empty($shopIds) || $commission < 0 || $commission > 50) {
                        $response['message'] = 'Invalid shop selection or commission rate.';
                        break;
                    }

                    $placeholders = str_repeat('?,', count($shopIds) - 1) . '?';
                    $stmt = $pdo->prepare("UPDATE laundry_shops SET commission_percentage = ?, commission_updated_at = NOW() WHERE id IN ($placeholders)");
                    $params = array_merge([$commission], $shopIds);

                    if ($stmt->execute($params)) {
                        logAdminAction($pdo, $_SESSION['admin_id'], 'bulk_commission_update', "Updated " . count($shopIds) . " shops to $commission%");
                        $response = ['success' => true, 'message' => 'Commission rates updated for ' . count($shopIds) . ' shops!'];
                    } else {
                        $response['message'] = 'Failed to update commission rates.';
                    }
                    break;

                case 'create_shop_owner':
                    $shopId = intval($_POST['shop_id']);
                    $username = sanitize($_POST['username']);
                    $password = $_POST['password'];

                    // Get shop details
                    $shopStmt = $pdo->prepare("SELECT * FROM laundry_shops WHERE id = ?");
                    $shopStmt->execute([$shopId]);
                    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);

                    if ($shop) {
                        // Check if shop owner already exists
                        if (!$shopOwnerManager->shopOwnerExistsByShopId($shopId)) {
                            $shopOwnerId = $shopOwnerManager->createShopOwner(
                                $shopId,
                                $username,
                                $password,
                                $shop['email'],
                                $shop['phone'],
                                $shop['owner_name']
                            );

                            if ($shopOwnerId) {
                                // Verify the shop owner account immediately
                                $shopOwnerManager->verifyShopOwner($shopOwnerId);
                                logAdminAction($pdo, $_SESSION['admin_id'], 'shop_owner_created', "Shop ID: $shopId, Username: $username", $shopId);
                                $response = ['success' => true, 'message' => "Shop owner account created successfully! Username: $username"];
                            } else {
                                $response['message'] = 'Failed to create shop owner account. Username may already exist.';
                            }
                        } else {
                            $response['message'] = 'Shop owner account already exists for this shop.';
                        }
                    } else {
                        $response['message'] = 'Shop not found.';
                    }
                    break;

                case 'add_admin_note':
                    $shopId = intval($_POST['shop_id']);
                    $note = sanitize($_POST['admin_note']);

                    $stmt = $pdo->prepare("
                        INSERT INTO shop_admin_notes (shop_id, admin_id, note, created_at)
                        VALUES (?, ?, ?, NOW())
                    ");
                    if ($stmt->execute([$shopId, $_SESSION['admin_id'], $note])) {
                        $response = ['success' => true, 'message' => 'Admin note added successfully!'];
                    } else {
                        $response['message'] = 'Failed to add admin note.';
                    }
                    break;

                case 'update_withdrawal_status':
                    $requestId = intval($_POST['request_id']);
                    $newStatus = sanitize($_POST['new_status']);
                    $adminNotes = sanitize($_POST['admin_notes'] ?? '');
                    $rejectionReason = sanitize($_POST['rejection_reason'] ?? '');
                    $transactionRef = sanitize($_POST['transaction_reference'] ?? '');

                    $updateData = [
                        'admin_notes' => $adminNotes,
                        'change_reason' => $rejectionReason ?: 'Status updated by admin'
                    ];

                    if ($newStatus === 'completed' || $newStatus === 'approved') {
                        $updateData['transaction_reference'] = $transactionRef;
                    }

                    if ($newStatus === 'rejected') {
                        $updateData['rejection_reason'] = $rejectionReason;
                    }

                    try {
                        $paymentWithdrawalManager->updateRequestStatus($requestId, $newStatus, $_SESSION['admin_id'], $updateData);
                        logAdminAction($pdo, $_SESSION['admin_id'], 'withdrawal_status_updated', "Request ID: $requestId, Status: $newStatus");
                        $response = ['success' => true, 'message' => 'Withdrawal request status updated successfully!'];
                    } catch (Exception $e) {
                        $response['message'] = $e->getMessage();
                    }
                    break;

                case 'bulk_update_withdrawal_status':
                    $requestIds = $_POST['request_ids'] ?? [];
                    $newStatus = sanitize($_POST['bulk_status']);
                    $adminNotes = sanitize($_POST['bulk_admin_notes'] ?? '');

                    if (empty($requestIds)) {
                        $response['message'] = 'No withdrawal requests selected.';
                        break;
                    }

                    $successCount = 0;
                    $errors = [];

                    foreach ($requestIds as $requestId) {
                        try {
                            $updateData = [
                                'admin_notes' => $adminNotes,
                                'change_reason' => 'Bulk status update by admin'
                            ];

                            $paymentWithdrawalManager->updateRequestStatus(intval($requestId), $newStatus, $_SESSION['admin_id'], $updateData);
                            $successCount++;
                        } catch (Exception $e) {
                            $errors[] = "Request ID $requestId: " . $e->getMessage();
                        }
                    }

                    if ($successCount > 0) {
                        logAdminAction($pdo, $_SESSION['admin_id'], 'bulk_withdrawal_status_update', "Updated $successCount requests to $newStatus");
                        $response = ['success' => true, 'message' => "Successfully updated $successCount withdrawal requests."];
                        if (!empty($errors)) {
                            $response['message'] .= ' Some requests failed: ' . implode(', ', $errors);
                        }
                    } else {
                        $response['message'] = 'Failed to update withdrawal requests: ' . implode(', ', $errors);
                    }
                    break;
            }
        } catch (Exception $e) {
            $response['message'] = 'An error occurred: ' . $e->getMessage();
        }
    }

    // Return JSON response for AJAX requests
    if (isset($_POST['ajax']) && $_POST['ajax'] === '1') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    } else {
        // Set session messages for regular form submissions
        if ($response['success']) {
            $success = $response['message'];
        } else {
            $error = $response['message'];
        }
    }
}

// Helper functions
function getDashboardStats($pdo) {
    $stats = [];

    // Total shops
    $stmt = $pdo->query("SELECT COUNT(*) FROM laundry_shops");
    $stats['total_shops'] = $stmt->fetchColumn();

    // Active shops
    $stmt = $pdo->query("SELECT COUNT(*) FROM laundry_shops WHERE is_active = 1");
    $stats['active_shops'] = $stmt->fetchColumn();

    // Verified shops
    $stmt = $pdo->query("SELECT COUNT(*) FROM laundry_shops WHERE is_verified = 1");
    $stats['verified_shops'] = $stmt->fetchColumn();

    // Total revenue this month
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(o.total), 0)
        FROM orders o
        JOIN laundry_shops ls ON o.shop_id = ls.id
        WHERE o.status = 'delivered'
        AND MONTH(o.created_at) = MONTH(CURRENT_DATE())
        AND YEAR(o.created_at) = YEAR(CURRENT_DATE())
    ");
    $stats['monthly_revenue'] = $stmt->fetchColumn();

    // Total commission this month
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(ct.platform_earnings), 0)
        FROM commission_transactions ct
        WHERE MONTH(ct.created_at) = MONTH(CURRENT_DATE())
        AND YEAR(ct.created_at) = YEAR(CURRENT_DATE())
    ");
    $stats['monthly_commission'] = $stmt->fetchColumn();

    // Pending verifications
    $stmt = $pdo->query("SELECT COUNT(*) FROM laundry_shops WHERE is_verified = 0");
    $stats['pending_verifications'] = $stmt->fetchColumn();

    // Withdrawal request statistics
    $stmt = $pdo->query("SELECT COUNT(*) FROM payment_withdrawal_requests WHERE status = 'pending'");
    $stats['pending_withdrawals'] = $stmt->fetchColumn();

    $stmt = $pdo->query("
        SELECT COALESCE(SUM(request_amount), 0)
        FROM payment_withdrawal_requests
        WHERE status IN ('pending', 'processing')
    ");
    $stats['pending_withdrawal_amount'] = $stmt->fetchColumn();

    $stmt = $pdo->query("
        SELECT COALESCE(SUM(final_amount), 0)
        FROM payment_withdrawal_requests
        WHERE status = 'completed'
        AND MONTH(completed_at) = MONTH(CURRENT_DATE())
        AND YEAR(completed_at) = YEAR(CURRENT_DATE())
    ");
    $stats['monthly_withdrawals'] = $stmt->fetchColumn();

    return $stats;
}

function getShopDetailedStats($pdo, $shopId) {
    $stats = [];

    // Basic shop info
    $stmt = $pdo->prepare("
        SELECT ls.*, d.name as division_name, dist.name as district_name, up.name as upazilla_name
        FROM laundry_shops ls
        LEFT JOIN divisions d ON ls.division_id = d.id
        LEFT JOIN districts dist ON ls.district_id = dist.id
        LEFT JOIN upazillas up ON ls.upazilla_id = up.id
        WHERE ls.id = ?
    ");
    $stmt->execute([$shopId]);
    $stats['shop'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Order statistics
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total ELSE 0 END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN status = 'delivered' THEN total ELSE NULL END), 0) as avg_order_value
        FROM orders
        WHERE shop_id = ?
    ");
    $stmt->execute([$shopId]);
    $stats['orders'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Monthly revenue trend (last 6 months)
    $stmt = $pdo->prepare("
        SELECT
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as orders,
            COALESCE(SUM(total), 0) as revenue
        FROM orders
        WHERE shop_id = ? AND status = 'delivered'
        AND created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
    ");
    $stmt->execute([$shopId]);
    $stats['monthly_trend'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $stats;
}

function logAdminAction($pdo, $adminId, $action, $description, $shopId = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity_logs (admin_id, action, description, shop_id, ip_address, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$adminId, $action, $description, $shopId, $_SERVER['REMOTE_ADDR'] ?? '']);
    } catch (Exception $e) {
        // Log error but don't break the main functionality
        error_log("Failed to log admin action: " . $e->getMessage());
    }
}

// Get dashboard statistics
$dashboardStats = getDashboardStats($pdo);

// Get shops with enhanced pagination and filtering
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 15;
$offset = ($page - 1) * $limit;

$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$commission_filter = isset($_GET['commission_filter']) ? $_GET['commission_filter'] : 'all';
$sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'created_at';
$sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'DESC';

// Build query with enhanced filtering
$whereClause = "WHERE 1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND (ls.name LIKE ? OR ls.owner_name LIKE ? OR ls.phone LIKE ? OR ls.email LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($status !== 'all') {
    switch ($status) {
        case 'active':
            $whereClause .= " AND ls.is_active = 1";
            break;
        case 'inactive':
            $whereClause .= " AND ls.is_active = 0";
            break;
        case 'verified':
            $whereClause .= " AND ls.is_verified = 1";
            break;
        case 'unverified':
            $whereClause .= " AND ls.is_verified = 0";
            break;
        case 'pending_approval':
            $whereClause .= " AND ls.is_verified = 0 AND ls.is_active = 1";
            break;
    }
}

if ($commission_filter !== 'all') {
    switch ($commission_filter) {
        case 'high':
            $whereClause .= " AND ls.commission_percentage >= 20";
            break;
        case 'medium':
            $whereClause .= " AND ls.commission_percentage >= 10 AND ls.commission_percentage < 20";
            break;
        case 'low':
            $whereClause .= " AND ls.commission_percentage < 10";
            break;
    }
}

// Validate sort parameters
$allowedSortFields = ['created_at', 'name', 'rating', 'commission_percentage', 'total_orders', 'total_revenue'];
$allowedSortOrders = ['ASC', 'DESC'];

if (!in_array($sort_by, $allowedSortFields)) {
    $sort_by = 'created_at';
}
if (!in_array($sort_order, $allowedSortOrders)) {
    $sort_order = 'DESC';
}

// Get total count
$countSql = "SELECT COUNT(*) FROM laundry_shops ls $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalShops = $countStmt->fetchColumn();
$totalPages = ceil($totalShops / $limit);

// Get shops with comprehensive data
$sql = "
    SELECT
        ls.*,
        d.name as division_name,
        dist.name as district_name,
        up.name as upazilla_name,
        COUNT(DISTINCT o.id) as total_orders,
        COUNT(DISTINCT CASE WHEN o.status = 'delivered' THEN o.id END) as completed_orders,
        COUNT(DISTINCT CASE WHEN o.status IN ('pending', 'processing') THEN o.id END) as active_orders,
        COALESCE(SUM(CASE WHEN o.status = 'delivered' THEN o.total ELSE 0 END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN o.status = 'delivered' THEN o.shop_commission ELSE 0 END), 0) as total_commission_earned,
        so.id as shop_owner_id,
        so.username as shop_owner_username,
        so.is_verified as shop_owner_verified,
        so.last_login as shop_owner_last_login,
        (SELECT COUNT(*) FROM shop_admin_notes WHERE shop_id = ls.id) as admin_notes_count
    FROM laundry_shops ls
    LEFT JOIN divisions d ON ls.division_id = d.id
    LEFT JOIN districts dist ON ls.district_id = dist.id
    LEFT JOIN upazillas up ON ls.upazilla_id = up.id
    LEFT JOIN orders o ON ls.id = o.shop_id
    LEFT JOIN shop_owners so ON ls.id = so.shop_id
    $whereClause
    GROUP BY ls.id
    ORDER BY ls.$sort_by $sort_order
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$shops = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<!-- Custom Styles for Enhanced Dashboard -->
<style>
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.shop-table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.shop-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
}

.shop-avatar-placeholder {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
}

.commission-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.action-btn {
    border: none;
    border-radius: 8px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-btn i {
    font-size: 0.875rem;
}

.filter-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    border-radius: 15px;
}

.table-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="spinner"></div>
</div>

<!-- Page Header -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
    <div>
        <h1 class="h2 mb-0">Shop Management Dashboard</h1>
        <p class="text-muted mb-0">Comprehensive shop management and analytics</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="exportShopsData()">
                <i class="fas fa-download"></i> Export
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addShopModal">
            <i class="fas fa-plus"></i> Add New Shop
        </button>
    </div>
</div>

<!-- Alert Messages -->
<?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Dashboard Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Shops</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($dashboardStats['total_shops']) ?></div>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon bg-gradient-primary">
                            <i class="fas fa-store"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Shops</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($dashboardStats['active_shops']) ?></div>
                        <div class="text-xs text-muted">
                            <?= $dashboardStats['total_shops'] > 0 ? round(($dashboardStats['active_shops'] / $dashboardStats['total_shops']) * 100, 1) : 0 ?>% of total
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon bg-gradient-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Monthly Revenue</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">৳<?= number_format($dashboardStats['monthly_revenue'], 2) ?></div>
                        <div class="text-xs text-muted">Commission: ৳<?= number_format($dashboardStats['monthly_commission'], 2) ?></div>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon bg-gradient-info">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Approval</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($dashboardStats['pending_verifications']) ?></div>
                        <?php if ($dashboardStats['pending_verifications'] > 0): ?>
                            <div class="text-xs text-warning">Requires attention</div>
                        <?php else: ?>
                            <div class="text-xs text-success">All up to date</div>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon bg-gradient-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Withdrawal Requests Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>Payment Withdrawal Requests
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshWithdrawalRequests()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" onclick="showWithdrawalFilters()">
                        <i class="fas fa-filter"></i> Filters
                    </button>
                </div>
            </div>

            <!-- Withdrawal Statistics Cards -->
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <div class="h4 mb-0"><?= number_format($dashboardStats['pending_withdrawals']) ?></div>
                                <div class="small">Pending Requests</div>
                                <div class="small">৳<?= number_format($dashboardStats['pending_withdrawal_amount'], 2) ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <div class="h4 mb-0">৳<?= number_format($dashboardStats['monthly_withdrawals'], 2) ?></div>
                                <div class="small">This Month</div>
                                <div class="small">Completed Withdrawals</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <div class="h4 mb-0" id="processing_count">-</div>
                                <div class="small">Processing</div>
                                <div class="small">In Progress</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <div class="h4 mb-0" id="rejected_count">-</div>
                                <div class="small">Rejected</div>
                                <div class="small">This Month</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Requests Filters -->
                <div class="card mb-3 d-none" id="withdrawalFiltersCard">
                    <div class="card-body">
                        <form id="withdrawalFiltersForm" class="row g-3">
                            <div class="col-md-2">
                                <label for="withdrawal_status_filter" class="form-label">Status</label>
                                <select class="form-select" id="withdrawal_status_filter" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="approved">Approved</option>
                                    <option value="completed">Completed</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="withdrawal_method_filter" class="form-label">Payment Method</label>
                                <select class="form-select" id="withdrawal_method_filter" name="payment_method">
                                    <option value="">All Methods</option>
                                    <option value="bkash">bKash</option>
                                    <option value="nagad">Nagad</option>
                                    <option value="rocket">Rocket</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="withdrawal_date_from" class="form-label">Date From</label>
                                <input type="date" class="form-control" id="withdrawal_date_from" name="date_from">
                            </div>
                            <div class="col-md-2">
                                <label for="withdrawal_date_to" class="form-label">Date To</label>
                                <input type="date" class="form-control" id="withdrawal_date_to" name="date_to">
                            </div>
                            <div class="col-md-2">
                                <label for="withdrawal_shop_filter" class="form-label">Shop</label>
                                <select class="form-select" id="withdrawal_shop_filter" name="shop_id">
                                    <option value="">All Shops</option>
                                    <?php foreach ($shops as $shop): ?>
                                        <option value="<?= $shop['id'] ?>"><?= htmlspecialchars($shop['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Withdrawal Requests Table -->
                <div class="table-responsive">
                    <table class="table table-hover" id="withdrawalRequestsTable">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" class="form-check-input" id="selectAllWithdrawals">
                                </th>
                                <th>Request ID</th>
                                <th>Shop</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="withdrawalRequestsBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Loading withdrawal requests...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Withdrawal Requests Pagination -->
                <nav aria-label="Withdrawal requests pagination" id="withdrawalPagination" class="d-none">
                    <ul class="pagination justify-content-center">
                        <!-- Pagination will be populated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="card filter-card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3" id="filterForm">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?= htmlspecialchars($search) ?>" placeholder="Shop name, owner, phone, email...">
                </div>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Shops</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="verified" <?= $status === 'verified' ? 'selected' : '' ?>>Verified</option>
                    <option value="unverified" <?= $status === 'unverified' ? 'selected' : '' ?>>Unverified</option>
                    <option value="pending_approval" <?= $status === 'pending_approval' ? 'selected' : '' ?>>Pending Approval</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="commission_filter" class="form-label">Commission</label>
                <select class="form-select" id="commission_filter" name="commission_filter">
                    <option value="all" <?= $commission_filter === 'all' ? 'selected' : '' ?>>All Rates</option>
                    <option value="high" <?= $commission_filter === 'high' ? 'selected' : '' ?>>High (≥20%)</option>
                    <option value="medium" <?= $commission_filter === 'medium' ? 'selected' : '' ?>>Medium (10-19%)</option>
                    <option value="low" <?= $commission_filter === 'low' ? 'selected' : '' ?>>Low (<10%)</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort_by" class="form-label">Sort By</label>
                <select class="form-select" id="sort_by" name="sort_by">
                    <option value="created_at" <?= $sort_by === 'created_at' ? 'selected' : '' ?>>Date Added</option>
                    <option value="name" <?= $sort_by === 'name' ? 'selected' : '' ?>>Shop Name</option>
                    <option value="rating" <?= $sort_by === 'rating' ? 'selected' : '' ?>>Rating</option>
                    <option value="commission_percentage" <?= $sort_by === 'commission_percentage' ? 'selected' : '' ?>>Commission</option>
                    <option value="total_orders" <?= $sort_by === 'total_orders' ? 'selected' : '' ?>>Orders</option>
                    <option value="total_revenue" <?= $sort_by === 'total_revenue' ? 'selected' : '' ?>>Revenue</option>
                </select>
            </div>
            <div class="col-md-1">
                <label for="sort_order" class="form-label">Order</label>
                <select class="form-select" id="sort_order" name="sort_order">
                    <option value="DESC" <?= $sort_order === 'DESC' ? 'selected' : '' ?>>↓</option>
                    <option value="ASC" <?= $sort_order === 'ASC' ? 'selected' : '' ?>>↑</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary flex-fill">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                    <a href="shops.php" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Shops Table -->
<div class="card table-container">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="card-title mb-0">
                <i class="fas fa-store me-2"></i>Shops Management
            </h5>
            <small class="text-muted">
                Showing <?= count($shops) ?> of <?= number_format($totalShops) ?> shops
                <?php if (!empty($search) || $status !== 'all' || $commission_filter !== 'all'): ?>
                    (filtered)
                <?php endif; ?>
            </small>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleBulkActions()">
                <i class="fas fa-tasks"></i> Bulk Actions
            </button>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog"></i> Options
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportShopsData()">
                        <i class="fas fa-download"></i> Export Data
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="printShopsList()">
                        <i class="fas fa-print"></i> Print List
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="refreshTable()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Bar (Hidden by default) -->
    <div class="card-body border-bottom bg-light d-none" id="bulkActionsBar">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <input type="checkbox" class="form-check-input me-2" id="selectAllShops">
                    <label class="form-check-label me-3" for="selectAllShops">Select All</label>
                    <span class="badge bg-primary" id="selectedCount">0 selected</span>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="bulkVerifyShops()">
                        <i class="fas fa-check"></i> Verify Selected
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showBulkCommissionModal()">
                        <i class="fas fa-percentage"></i> Update Commission
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="bulkDeactivateShops()">
                        <i class="fas fa-ban"></i> Deactivate Selected
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card-body p-0">
        <?php if (empty($shops)): ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-store fa-4x text-muted"></i>
                </div>
                <h5 class="text-muted">No shops found</h5>
                <p class="text-muted mb-4">
                    <?php if (!empty($search) || $status !== 'all' || $commission_filter !== 'all'): ?>
                        Try adjusting your filters or search terms.
                    <?php else: ?>
                        Get started by adding your first shop.
                    <?php endif; ?>
                </p>
                <?php if (empty($search) && $status === 'all' && $commission_filter === 'all'): ?>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addShopModal">
                        <i class="fas fa-plus"></i> Add First Shop
                    </button>
                <?php else: ?>
                    <a href="shops.php" class="btn btn-outline-primary">
                        <i class="fas fa-undo"></i> Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover shop-table mb-0">
                    <thead>
                        <tr>
                            <th width="40" class="d-none" id="bulkSelectColumn">
                                <input type="checkbox" class="form-check-input" id="selectAllHeader">
                            </th>
                            <th width="250">Shop Information</th>
                            <th width="150">Owner & Contact</th>
                            <th width="120">Location</th>
                            <th width="100">Performance</th>
                            <th width="100">Commission</th>
                            <th width="120">Orders & Revenue</th>
                            <th width="120">Status</th>
                            <th width="100">Last Activity</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($shops as $shop): ?>
                            <tr data-shop-id="<?= $shop['id'] ?>" class="shop-row">
                                <td class="d-none bulk-select-cell">
                                    <input type="checkbox" class="form-check-input shop-checkbox" value="<?= $shop['id'] ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if ($shop['profile_image_url']): ?>
                                            <img src="<?= htmlspecialchars($shop['profile_image_url']) ?>"
                                                 class="shop-avatar me-3" alt="Shop Image">
                                        <?php else: ?>
                                            <div class="shop-avatar-placeholder me-3">
                                                <i class="fas fa-store"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="fw-bold text-dark mb-1">
                                                <?= htmlspecialchars($shop['name']) ?>
                                                <?php if ($shop['admin_notes_count'] > 0): ?>
                                                    <i class="fas fa-sticky-note text-warning ms-1"
                                                       title="Has admin notes" data-bs-toggle="tooltip"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-muted small">
                                                <i class="fas fa-phone me-1"></i><?= htmlspecialchars($shop['phone']) ?>
                                            </div>
                                            <?php if ($shop['email']): ?>
                                                <div class="text-muted small">
                                                    <i class="fas fa-envelope me-1"></i><?= htmlspecialchars($shop['email']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold"><?= htmlspecialchars($shop['owner_name']) ?></div>
                                    <?php if ($shop['shop_owner_id']): ?>
                                        <div class="small text-success">
                                            <i class="fas fa-user-check me-1"></i>
                                            <?= htmlspecialchars($shop['shop_owner_username']) ?>
                                        </div>
                                        <?php if ($shop['shop_owner_last_login']): ?>
                                            <div class="small text-muted">
                                                Last: <?= date('M j, Y', strtotime($shop['shop_owner_last_login'])) ?>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="small text-muted">
                                            <i class="fas fa-user-times me-1"></i>No account
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="small">
                                        <?php if ($shop['upazilla_name'] || $shop['district_name']): ?>
                                            <div><?= htmlspecialchars($shop['upazilla_name'] ?? '') ?></div>
                                            <div class="text-muted"><?= htmlspecialchars($shop['district_name'] ?? '') ?></div>
                                        <?php else: ?>
                                            <span class="text-muted">Location not set</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center mb-1">
                                        <div class="me-2">
                                            <?php
                                            $ratingClass = 'bg-secondary';
                                            if ($shop['rating'] >= 4.5) $ratingClass = 'bg-success';
                                            elseif ($shop['rating'] >= 4.0) $ratingClass = 'bg-primary';
                                            elseif ($shop['rating'] >= 3.5) $ratingClass = 'bg-warning';
                                            elseif ($shop['rating'] >= 3.0) $ratingClass = 'bg-orange';
                                            else $ratingClass = 'bg-danger';
                                            ?>
                                            <span class="badge <?= $ratingClass ?> status-badge">
                                                <i class="fas fa-star"></i> <?= number_format($shop['rating'], 1) ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="small text-muted">
                                        <?= number_format($shop['total_reviews']) ?> reviews
                                    </div>
                                </td>
                                <td>
                                    <div class="commission-badge">
                                        <?= number_format($shop['commission_percentage'], 1) ?>%
                                    </div>
                                    <div class="small text-muted mt-1">
                                        Earned: ৳<?= number_format($shop['total_commission_earned'], 0) ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold text-primary">
                                        <?= number_format($shop['total_orders']) ?> orders
                                    </div>
                                    <div class="small text-success">
                                        ৳<?= number_format($shop['total_revenue'], 0) ?>
                                    </div>
                                    <?php if ($shop['active_orders'] > 0): ?>
                                        <div class="small text-warning">
                                            <?= $shop['active_orders'] ?> active
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <?php if ($shop['is_active']): ?>
                                            <span class="badge bg-success status-badge">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger status-badge">Inactive</span>
                                        <?php endif; ?>

                                        <?php if ($shop['is_verified']): ?>
                                            <span class="badge bg-primary status-badge">Verified</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning status-badge">Pending</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="small text-muted">
                                        Added: <?= date('M j, Y', strtotime($shop['created_at'])) ?>
                                    </div>
                                    <?php if ($shop['updated_at'] && $shop['updated_at'] !== $shop['created_at']): ?>
                                        <div class="small text-muted">
                                            Updated: <?= date('M j', strtotime($shop['updated_at'])) ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <!-- Direct View Details Button -->
                                        <a href="shop_details.php?id=<?= $shop['id'] ?>" target="_blank"
                                           class="btn btn-sm btn-outline-info action-btn" title="View Shop Details">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <!-- Actions Dropdown -->
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle action-btn"
                                                    data-bs-toggle="dropdown" aria-expanded="false" title="More Actions">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="shop_details.php?id=<?= $shop['id'] ?>" target="_blank">
                                                    <i class="fas fa-eye me-2"></i>View Details
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="showShopAnalytics(<?= $shop['id'] ?>)">
                                                    <i class="fas fa-chart-bar me-2"></i>Analytics
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="edit_shop.php?id=<?= $shop['id'] ?>">
                                                    <i class="fas fa-edit me-2"></i>Edit Shop
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>

                                            <?php if (!$shop['is_verified']): ?>
                                                <li>
                                                    <a class="dropdown-item text-success" href="#"
                                                       onclick="verifyShop(<?= $shop['id'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                        <i class="fas fa-check me-2"></i>Verify Shop
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <li>
                                                <a class="dropdown-item text-info" href="#"
                                                   onclick="updateCommission(<?= $shop['id'] ?>, <?= $shop['commission_percentage'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                    <i class="fas fa-percentage me-2"></i>Update Commission
                                                </a>
                                            </li>

                                            <?php if ($shop['is_verified'] && !$shop['shop_owner_id']): ?>
                                                <li>
                                                    <a class="dropdown-item text-success" href="#"
                                                       onclick="createShopOwner(<?= $shop['id'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                        <i class="fas fa-user-plus me-2"></i>Create Owner Account
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <li>
                                                <a class="dropdown-item" href="#"
                                                   onclick="showAdminNotes(<?= $shop['id'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                    <i class="fas fa-sticky-note me-2"></i>Admin Notes
                                                    <?php if ($shop['admin_notes_count'] > 0): ?>
                                                        <span class="badge bg-warning ms-1"><?= $shop['admin_notes_count'] ?></span>
                                                    <?php endif; ?>
                                                </a>
                                            </li>

                                            <li><hr class="dropdown-divider"></li>

                                            <?php if ($shop['is_active']): ?>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#"
                                                       onclick="deactivateShop(<?= $shop['id'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                        <i class="fas fa-ban me-2"></i>Deactivate
                                                    </a>
                                                </li>
                                            <?php else: ?>
                                                <li>
                                                    <a class="dropdown-item text-success" href="#"
                                                       onclick="activateShop(<?= $shop['id'] ?>, '<?= htmlspecialchars($shop['name']) ?>')">
                                                        <i class="fas fa-check-circle me-2"></i>Activate
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Enhanced Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <label for="limitSelect" class="form-label me-2 mb-0">Show:</label>
                                <select class="form-select form-select-sm" id="limitSelect" style="width: auto;" onchange="changeLimit(this.value)">
                                    <option value="15" <?= $limit == 15 ? 'selected' : '' ?>>15</option>
                                    <option value="25" <?= $limit == 25 ? 'selected' : '' ?>>25</option>
                                    <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50</option>
                                    <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100</option>
                                </select>
                                <span class="ms-2 text-muted">
                                    Showing <?= ($offset + 1) ?> to <?= min($offset + $limit, $totalShops) ?> of <?= number_format($totalShops) ?> entries
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="Shops pagination">
                                <ul class="pagination pagination-sm justify-content-end mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= buildPaginationUrl(1) ?>" aria-label="First">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= buildPaginationUrl($page - 1) ?>" aria-label="Previous">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    for ($i = $startPage; $i <= $endPage; $i++):
                                    ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="<?= buildPaginationUrl($i) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= buildPaginationUrl($page + 1) ?>" aria-label="Next">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="<?= buildPaginationUrl($totalPages) ?>" aria-label="Last">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
// Helper function for pagination URLs
function buildPaginationUrl($pageNum) {
    global $search, $status, $commission_filter, $sort_by, $sort_order, $limit;
    $params = [
        'page' => $pageNum,
        'search' => $search,
        'status' => $status,
        'commission_filter' => $commission_filter,
        'sort_by' => $sort_by,
        'sort_order' => $sort_order,
        'limit' => $limit
    ];
    return 'shops.php?' . http_build_query(array_filter($params));
}
?>

<!-- Enhanced Add Shop Modal -->
<div class="modal fade" id="addShopModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>Add New Shop
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="add_shop.php" id="addShopForm">
                <div class="modal-body">
                    <!-- Progress Steps -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div class="step active" data-step="1">
                                    <div class="step-icon">1</div>
                                    <div class="step-label">Basic Info</div>
                                </div>
                                <div class="step" data-step="2">
                                    <div class="step-icon">2</div>
                                    <div class="step-label">Location</div>
                                </div>
                                <div class="step" data-step="3">
                                    <div class="step-icon">3</div>
                                    <div class="step-label">Settings</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Basic Information -->
                    <div class="step-content" data-step="1">
                        <h6 class="mb-3">Basic Shop Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shop_name" class="form-label">Shop Name *</label>
                                    <input type="text" class="form-control" id="shop_name" name="shop_name" required>
                                    <div class="form-text">Enter the official name of the laundry shop</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="owner_name" class="form-label">Owner Name *</label>
                                    <input type="text" class="form-control" id="owner_name" name="owner_name" required>
                                    <div class="form-text">Full name of the shop owner</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required
                                           pattern="[0-9]{11}" placeholder="01XXXXXXXXX">
                                    <div class="form-text">11-digit mobile number (e.g., 01712345678)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="<EMAIL>">
                                    <div class="form-text">Optional: For notifications and communications</div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Shop Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Brief description of services offered..."></textarea>
                            <div class="form-text">Optional: Describe the services and specialties</div>
                        </div>
                    </div>

                    <!-- Step 2: Location -->
                    <div class="step-content d-none" data-step="2">
                        <h6 class="mb-3">Shop Location</h6>
                        <div class="mb-3">
                            <label for="address" class="form-label">Full Address *</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required
                                      placeholder="House/Building number, Street, Area, City..."></textarea>
                        </div>

                        <!-- Enhanced Location Picker -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>Set Precise Location
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="location_search"
                                               placeholder="Search for location or enter address...">
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary w-100" onclick="searchLocation()">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                </div>
                                <div id="map" style="height: 350px; border-radius: 8px; border: 1px solid #dee2e6;"></div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-12">
                                            <small class="text-muted d-block mb-2">
                                                <i class="fas fa-info-circle"></i> Click on the map to set shop location or use quick locations:
                                            </small>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(23.8103, 90.4125, 'Dhaka')">
                                                    Dhaka
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(22.3569, 91.7832, 'Chittagong')">
                                                    Chittagong
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(24.3636, 88.6241, 'Rajshahi')">
                                                    Rajshahi
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(24.8949, 91.8687, 'Sylhet')">
                                                    Sylhet
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-1 mb-1" onclick="goToLocation(25.7439, 89.2752, 'Rangpur')">
                                                    Rangpur
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="getCurrentLocation()">
                                                    <i class="fas fa-location-arrow"></i> My Location
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">Latitude *</label>
                                    <input type="number" class="form-control" id="latitude" name="latitude"
                                           step="any" min="-90" max="90" required onchange="updateMapFromCoordinates()">
                                    <div class="form-text">GPS coordinates for map location</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">Longitude *</label>
                                    <input type="number" class="form-control" id="longitude" name="longitude"
                                           step="any" min="-180" max="180" required onchange="updateMapFromCoordinates()">
                                    <div class="form-text">GPS coordinates for map location</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Settings -->
                    <div class="step-content d-none" data-step="3">
                        <h6 class="mb-3">Shop Settings & Configuration</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="commission_rate" name="commission_rate"
                                               min="0" max="50" step="0.1" value="15">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <div class="form-text">Platform commission percentage (0-50%)</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="is_verified" class="form-label">Verification Status</label>
                                    <select class="form-select" id="is_verified" name="is_verified">
                                        <option value="0">Unverified (Pending Review)</option>
                                        <option value="1">Verified (Approved)</option>
                                    </select>
                                    <div class="form-text">Shop verification status</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Shop Status</label>
                                    <select class="form-select" id="is_active" name="is_active">
                                        <option value="1">Active (Accepting Orders)</option>
                                        <option value="0">Inactive (Temporarily Closed)</option>
                                    </select>
                                    <div class="form-text">Current operational status</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Admin Notes</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                      placeholder="Internal notes about this shop..."></textarea>
                            <div class="form-text">Internal notes for administrative purposes</div>
                        </div>

                        <!-- Summary Card -->
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-clipboard-check me-2"></i>Shop Summary
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>Shop Name:</strong> <span id="summary_shop_name">-</span></p>
                                        <p class="mb-1"><strong>Owner:</strong> <span id="summary_owner_name">-</span></p>
                                        <p class="mb-1"><strong>Phone:</strong> <span id="summary_phone">-</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>Commission:</strong> <span id="summary_commission">15</span>%</p>
                                        <p class="mb-1"><strong>Status:</strong> <span id="summary_status">Active</span></p>
                                        <p class="mb-1"><strong>Verification:</strong> <span id="summary_verification">Unverified</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-outline-primary" id="prevStepBtn" onclick="previousStep()" style="display: none;">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextStep()">
                        Next <i class="fas fa-arrow-right"></i>
                    </button>
                    <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                        <i class="fas fa-plus-circle"></i> Add Shop
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced Commission Update Modal -->
<div class="modal fade" id="commissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-percentage me-2"></i>Update Commission Rate
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="commissionForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_commission">
                    <input type="hidden" name="shop_id" id="commission_shop_id">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Updating commission for: <strong id="commission_shop_name"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="commission_percentage" class="form-label">Commission Percentage</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="commission_percentage"
                                   name="commission_percentage" min="0" max="50" step="0.1" required>
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="form-text">Enter commission percentage (0-50%)</div>
                    </div>

                    <div class="mb-3">
                        <label for="effective_date" class="form-label">Effective Date</label>
                        <input type="date" class="form-control" id="effective_date" name="effective_date"
                               value="<?= date('Y-m-d') ?>">
                        <div class="form-text">When this commission rate should take effect</div>
                    </div>

                    <div class="mb-3">
                        <label for="commission_notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="commission_notes" name="commission_notes" rows="2"
                                  placeholder="Reason for commission change..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save"></i> Update Commission
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Commission Update Modal -->
<div class="modal fade" id="bulkCommissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-percentage me-2"></i>Bulk Commission Update
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="bulkCommissionForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="bulk_update_commission">
                    <input type="hidden" name="shop_ids" id="bulk_shop_ids">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will update commission rates for <strong id="bulk_selected_count">0</strong> selected shops.
                    </div>

                    <div class="mb-3">
                        <label for="bulk_commission_percentage" class="form-label">New Commission Percentage</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="bulk_commission_percentage"
                                   name="bulk_commission_percentage" min="0" max="50" step="0.1" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bulk_effective_date" class="form-label">Effective Date</label>
                        <input type="date" class="form-control" id="bulk_effective_date" name="bulk_effective_date"
                               value="<?= date('Y-m-d') ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save"></i> Update All Selected
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced Create Shop Owner Modal -->
<div class="modal fade" id="createShopOwnerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Create Shop Owner Account
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="createShopOwnerForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_shop_owner">
                    <input type="hidden" name="shop_id" id="shop_owner_shop_id">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Creating account for shop: <strong id="shop_owner_shop_name"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="shop_owner_username" class="form-label">Username *</label>
                        <input type="text" class="form-control" id="shop_owner_username"
                               name="username" required minlength="3" maxlength="50">
                        <div class="form-text">Shop owner will use this to login (3-50 characters)</div>
                    </div>

                    <div class="mb-3">
                        <label for="shop_owner_password" class="form-label">Password *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="shop_owner_password"
                                   name="password" required minlength="8">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('shop_owner_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Minimum 8 characters</div>
                    </div>

                    <div class="mb-3">
                        <label for="shop_owner_password_confirm" class="form-label">Confirm Password *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="shop_owner_password_confirm" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('shop_owner_password_confirm')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">Passwords do not match</div>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="generateRandomPassword()">
                            <i class="fas fa-random"></i> Generate Random Password
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success" id="createShopOwnerBtn">
                        <i class="fas fa-user-plus"></i> Create Account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Shop Verification Modal -->
<div class="modal fade" id="verifyShopModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>Verify Shop
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="verifyShopForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="verify_shop">
                    <input type="hidden" name="shop_id" id="verify_shop_id">

                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Verifying shop: <strong id="verify_shop_name"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Verification Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                  placeholder="Add any notes about the verification process..."></textarea>
                        <div class="form-text">Optional: Notes about verification decision</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Verify Shop
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Shop Deactivation Modal -->
<div class="modal fade" id="deactivateShopModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-ban me-2"></i>Deactivate Shop
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="deactivateShopForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="deactivate_shop">
                    <input type="hidden" name="shop_id" id="deactivate_shop_id">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will deactivate shop: <strong id="deactivate_shop_name"></strong>
                        <br><small>The shop will no longer receive new orders until reactivated.</small>
                    </div>

                    <div class="mb-3">
                        <label for="deactivation_reason" class="form-label">Reason for Deactivation *</label>
                        <select class="form-select" id="deactivation_reason" name="deactivation_reason" required>
                            <option value="">Select a reason...</option>
                            <option value="Quality Issues">Quality Issues</option>
                            <option value="Customer Complaints">Customer Complaints</option>
                            <option value="Policy Violation">Policy Violation</option>
                            <option value="Temporary Closure">Temporary Closure</option>
                            <option value="Owner Request">Owner Request</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="deactivation_notes" class="form-label">Additional Notes</label>
                        <textarea class="form-control" id="deactivation_notes" name="deactivation_notes" rows="3"
                                  placeholder="Provide additional details about the deactivation..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-ban"></i> Deactivate Shop
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Admin Notes Modal -->
<div class="modal fade" id="adminNotesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-sticky-note me-2"></i>Admin Notes
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>Shop: <span id="notes_shop_name"></span></h6>
                </div>

                <!-- Existing Notes -->
                <div class="mb-4">
                    <h6>Existing Notes</h6>
                    <div id="existing_notes" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-muted">Loading notes...</div>
                    </div>
                </div>

                <!-- Add New Note -->
                <form id="addNoteForm">
                    <input type="hidden" name="action" value="add_admin_note">
                    <input type="hidden" name="shop_id" id="notes_shop_id">

                    <div class="mb-3">
                        <label for="admin_note" class="form-label">Add New Note</label>
                        <textarea class="form-control" id="admin_note" name="admin_note" rows="3"
                                  placeholder="Enter your note here..." required></textarea>
                    </div>

                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-plus"></i> Add Note
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Shop Analytics Modal -->
<div class="modal fade" id="shopAnalyticsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar me-2"></i>Shop Analytics
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>Analytics for: <span id="analytics_shop_name"></span></h6>
                </div>

                <div id="analytics_content">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading analytics data...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportAnalytics()">
                    <i class="fas fa-download"></i> Export Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Withdrawal Request Status Update Modal -->
<div class="modal fade" id="updateWithdrawalStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Update Withdrawal Request Status
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="updateWithdrawalStatusForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_withdrawal_status">
                    <input type="hidden" name="request_id" id="withdrawal_request_id">

                    <div class="alert alert-info">
                        <strong>Request ID:</strong> <span id="withdrawal_request_display_id"></span><br>
                        <strong>Shop:</strong> <span id="withdrawal_shop_name"></span><br>
                        <strong>Amount:</strong> ৳<span id="withdrawal_amount"></span><br>
                        <strong>Current Status:</strong> <span id="withdrawal_current_status"></span>
                    </div>

                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status *</label>
                        <select class="form-select" id="new_status" name="new_status" required>
                            <option value="">Select Status...</option>
                            <option value="processing">Processing</option>
                            <option value="approved">Approved</option>
                            <option value="completed">Completed</option>
                            <option value="rejected">Rejected</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="mb-3" id="transaction_reference_field" style="display: none;">
                        <label for="transaction_reference" class="form-label">Transaction Reference</label>
                        <input type="text" class="form-control" id="transaction_reference" name="transaction_reference"
                               placeholder="Enter transaction ID or reference number">
                        <div class="form-text">Required for approved/completed status</div>
                    </div>

                    <div class="mb-3" id="rejection_reason_field" style="display: none;">
                        <label for="rejection_reason" class="form-label">Rejection Reason *</label>
                        <select class="form-select" id="rejection_reason" name="rejection_reason">
                            <option value="">Select reason...</option>
                            <option value="Insufficient Balance">Insufficient Balance</option>
                            <option value="Invalid Account Details">Invalid Account Details</option>
                            <option value="Suspicious Activity">Suspicious Activity</option>
                            <option value="Policy Violation">Policy Violation</option>
                            <option value="Technical Issue">Technical Issue</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                  placeholder="Add any notes about this status update..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Withdrawal Request Details Modal -->
<div class="modal fade" id="withdrawalDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>Withdrawal Request Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="withdrawal_details_content">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading request details...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printWithdrawalDetails()">
                    <i class="fas fa-print"></i> Print
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Withdrawal Status Update Modal -->
<div class="modal fade" id="bulkWithdrawalStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-tasks me-2"></i>Bulk Status Update
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkWithdrawalStatusForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="bulk_update_withdrawal_status">
                    <input type="hidden" name="request_ids" id="bulk_withdrawal_request_ids">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will update status for <strong id="bulk_withdrawal_count">0</strong> selected requests.
                    </div>

                    <div class="mb-3">
                        <label for="bulk_status" class="form-label">New Status *</label>
                        <select class="form-select" id="bulk_status" name="bulk_status" required>
                            <option value="">Select Status...</option>
                            <option value="processing">Processing</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="bulk_admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="bulk_admin_notes" name="bulk_admin_notes" rows="3"
                                  placeholder="Add notes for all selected requests..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Update All Selected
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OpenStreetMap with Leaflet (Free Alternative) -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
// Enhanced JavaScript for Shop Management Dashboard
let map;
let marker;
let currentStep = 1;
let selectedShops = new Set();

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeTooltips();
    updateSummaryFields();
    loadWithdrawalRequests();
    loadWithdrawalStats();

    // Debug: Check if View Details buttons are present
    const viewDetailsButtons = document.querySelectorAll('a[href*="shop_details.php"]');
    console.log('Found', viewDetailsButtons.length, 'View Details buttons');

    // Debug: Check if dropdown buttons are working
    const dropdownButtons = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    console.log('Found', dropdownButtons.length, 'dropdown buttons');
});

// Initialize all event listeners
function initializeEventListeners() {
    // Add shop modal step navigation
    document.getElementById('addShopModal').addEventListener('shown.bs.modal', function () {
        setTimeout(initializeMap, 100);
        resetAddShopForm();
    });

    // Form field listeners for summary update
    ['shop_name', 'owner_name', 'phone', 'commission_rate', 'is_verified', 'is_active'].forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', updateSummaryFields);
            field.addEventListener('change', updateSummaryFields);
        }
    });

    // Bulk selection handlers
    document.getElementById('selectAllShops')?.addEventListener('change', toggleAllShops);
    document.getElementById('selectAllHeader')?.addEventListener('change', toggleAllShops);

    // Shop checkboxes
    document.querySelectorAll('.shop-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedShops);
    });

    // Password confirmation validation
    const passwordField = document.getElementById('shop_owner_password');
    const confirmField = document.getElementById('shop_owner_password_confirm');

    if (passwordField && confirmField) {
        confirmField.addEventListener('input', validatePasswordMatch);
        passwordField.addEventListener('input', function() {
            if (confirmField.value) {
                validatePasswordMatch.call(confirmField);
            }
        });
    }
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Step navigation for add shop modal
function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < 3) {
            currentStep++;
            showStep(currentStep);
        }
    }
}

function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
    }
}

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.step-content').forEach(content => {
        content.classList.add('d-none');
    });

    // Show current step
    document.querySelector(`[data-step="${step}"]`).classList.remove('d-none');

    // Update step indicators
    document.querySelectorAll('.step').forEach((stepEl, index) => {
        stepEl.classList.toggle('active', index + 1 === step);
        stepEl.classList.toggle('completed', index + 1 < step);
    });

    // Update buttons
    document.getElementById('prevStepBtn').style.display = step > 1 ? 'inline-block' : 'none';
    document.getElementById('nextStepBtn').style.display = step < 3 ? 'inline-block' : 'none';
    document.getElementById('submitBtn').style.display = step === 3 ? 'inline-block' : 'none';

    // Initialize map on step 2
    if (step === 2) {
        setTimeout(initializeMap, 100);
    }
}

function validateCurrentStep() {
    const currentStepEl = document.querySelector(`[data-step="${currentStep}"]`);
    const requiredFields = currentStepEl.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    return isValid;
}

function resetAddShopForm() {
    currentStep = 1;
    showStep(1);
    document.getElementById('addShopForm').reset();
    updateSummaryFields();
}

function updateSummaryFields() {
    const shopName = document.getElementById('shop_name')?.value || '-';
    const ownerName = document.getElementById('owner_name')?.value || '-';
    const phone = document.getElementById('phone')?.value || '-';
    const commission = document.getElementById('commission_rate')?.value || '15';
    const isActive = document.getElementById('is_active')?.value === '1' ? 'Active' : 'Inactive';
    const isVerified = document.getElementById('is_verified')?.value === '1' ? 'Verified' : 'Unverified';

    document.getElementById('summary_shop_name').textContent = shopName;
    document.getElementById('summary_owner_name').textContent = ownerName;
    document.getElementById('summary_phone').textContent = phone;
    document.getElementById('summary_commission').textContent = commission;
    document.getElementById('summary_status').textContent = isActive;
    document.getElementById('summary_verification').textContent = isVerified;
}

// Map functionality
function initializeMap() {
    if (map) {
        map.invalidateSize();
        return;
    }

    map = L.map('map').setView([23.8103, 90.4125], 13);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    map.on('click', function(e) {
        setMapLocation(e.latlng.lat, e.latlng.lng);
    });

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const userLocation = [position.coords.latitude, position.coords.longitude];
            map.setView(userLocation, 15);
        });
    }
}

function setMapLocation(lat, lng) {
    if (marker) {
        map.removeLayer(marker);
    }

    marker = L.marker([lat, lng], { draggable: true }).addTo(map);
    document.getElementById('latitude').value = lat.toFixed(8);
    document.getElementById('longitude').value = lng.toFixed(8);
    map.setView([lat, lng], map.getZoom());

    marker.on('dragend', function(e) {
        const position = e.target.getLatLng();
        document.getElementById('latitude').value = position.lat.toFixed(8);
        document.getElementById('longitude').value = position.lng.toFixed(8);
        reverseGeocode(position.lat, position.lng);
    });

    reverseGeocode(lat, lng);
}

function reverseGeocode(lat, lng) {
    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`)
        .then(response => response.json())
        .then(data => {
            if (data.display_name) {
                document.getElementById('address').value = data.display_name;
            }
        })
        .catch(error => console.error('Reverse geocoding failed:', error));
}

function searchLocation() {
    const searchText = document.getElementById('location_search').value;
    if (!searchText) return;

    fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchText)}&countrycodes=bd&limit=1`)
        .then(response => response.json())
        .then(data => {
            if (data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                map.setView([lat, lng], 15);
                setMapLocation(lat, lng);
                document.getElementById('address').value = result.display_name;
            } else {
                showAlert('Location not found. Please try a different search term.', 'warning');
            }
        })
        .catch(error => {
            console.error('Geocoding failed:', error);
            showAlert('Search failed. Please try again.', 'danger');
        });
}

function goToLocation(lat, lng, name) {
    if (map) {
        map.setView([lat, lng], 13);
        setMapLocation(lat, lng);
        document.getElementById('location_search').value = name;
    }
}

function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            if (map) {
                map.setView([lat, lng], 15);
                setMapLocation(lat, lng);
            }
        }, function(error) {
            showAlert('Unable to get your location. Please allow location access or search manually.', 'warning');
        });
    } else {
        showAlert('Geolocation is not supported by this browser.', 'warning');
    }
}

function updateMapFromCoordinates() {
    const lat = parseFloat(document.getElementById('latitude').value);
    const lng = parseFloat(document.getElementById('longitude').value);

    if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        if (map) {
            map.setView([lat, lng], 15);
            if (marker) map.removeLayer(marker);
            marker = L.marker([lat, lng], { draggable: true }).addTo(map);
            marker.on('dragend', function(e) {
                const position = e.target.getLatLng();
                document.getElementById('latitude').value = position.lat.toFixed(8);
                document.getElementById('longitude').value = position.lng.toFixed(8);
                reverseGeocode(position.lat, position.lng);
            });
            reverseGeocode(lat, lng);
        }
    }
}

// Enhanced modal functions
function updateCommission(shopId, currentCommission, shopName) {
    document.getElementById('commission_shop_id').value = shopId;
    document.getElementById('commission_percentage').value = currentCommission;
    document.getElementById('commission_shop_name').textContent = shopName;
    new bootstrap.Modal(document.getElementById('commissionModal')).show();
}

function createShopOwner(shopId, shopName) {
    document.getElementById('shop_owner_shop_id').value = shopId;
    document.getElementById('shop_owner_shop_name').textContent = shopName;

    const suggestedUsername = shopName.toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 15) + '_owner';
    document.getElementById('shop_owner_username').value = suggestedUsername;

    document.getElementById('shop_owner_password').value = '';
    document.getElementById('shop_owner_password_confirm').value = '';

    new bootstrap.Modal(document.getElementById('createShopOwnerModal')).show();
}

function verifyShop(shopId, shopName) {
    document.getElementById('verify_shop_id').value = shopId;
    document.getElementById('verify_shop_name').textContent = shopName;
    new bootstrap.Modal(document.getElementById('verifyShopModal')).show();
}

function deactivateShop(shopId, shopName) {
    document.getElementById('deactivate_shop_id').value = shopId;
    document.getElementById('deactivate_shop_name').textContent = shopName;
    new bootstrap.Modal(document.getElementById('deactivateShopModal')).show();
}

function activateShop(shopId, shopName) {
    if (confirm(`Are you sure you want to activate "${shopName}"?`)) {
        submitAction('activate_shop', { shop_id: shopId });
    }
}

function showAdminNotes(shopId, shopName) {
    document.getElementById('notes_shop_id').value = shopId;
    document.getElementById('notes_shop_name').textContent = shopName;
    loadAdminNotes(shopId);
    new bootstrap.Modal(document.getElementById('adminNotesModal')).show();
}

function showShopAnalytics(shopId) {
    loadShopAnalytics(shopId);
    new bootstrap.Modal(document.getElementById('shopAnalyticsModal')).show();
}

function viewShopDetails(shopId) {
    console.log('viewShopDetails called with shopId:', shopId);

    try {
        // Validate shopId
        if (!shopId || isNaN(shopId)) {
            console.error('Invalid shop ID:', shopId);
            showAlert('Invalid shop ID', 'danger');
            return;
        }

        // Open shop details in a new tab
        const url = `shop_details.php?id=${shopId}`;
        console.log('Opening URL:', url);

        const newWindow = window.open(url, '_blank');

        // Check if popup was blocked
        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
            console.warn('Popup blocked, using fallback navigation');
            // Fallback: navigate in same window
            window.location.href = url;
        } else {
            console.log('Shop details opened successfully in new tab');
        }
    } catch (error) {
        console.error('Error opening shop details:', error);
        showAlert('Error opening shop details: ' + error.message, 'danger');
        // Fallback: navigate in same window
        window.location.href = `shop_details.php?id=${shopId}`;
    }
}

// Utility functions
function validatePasswordMatch() {
    const password = document.getElementById('shop_owner_password').value;
    const confirmPassword = this.value;
    const submitBtn = document.getElementById('createShopOwnerBtn');

    if (password !== confirmPassword) {
        this.classList.add('is-invalid');
        submitBtn.disabled = true;
    } else {
        this.classList.remove('is-invalid');
        submitBtn.disabled = false;
    }
}

function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = field.nextElementSibling.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.replace('fa-eye', 'fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.replace('fa-eye-slash', 'fa-eye');
    }
}

function generateRandomPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    document.getElementById('shop_owner_password').value = password;
    document.getElementById('shop_owner_password_confirm').value = password;
    validatePasswordMatch.call(document.getElementById('shop_owner_password_confirm'));
}

// Bulk operations
function toggleBulkActions() {
    const bulkBar = document.getElementById('bulkActionsBar');
    const selectColumn = document.getElementById('bulkSelectColumn');
    const selectCells = document.querySelectorAll('.bulk-select-cell');

    if (bulkBar.classList.contains('d-none')) {
        bulkBar.classList.remove('d-none');
        selectColumn.classList.remove('d-none');
        selectCells.forEach(cell => cell.classList.remove('d-none'));
    } else {
        bulkBar.classList.add('d-none');
        selectColumn.classList.add('d-none');
        selectCells.forEach(cell => cell.classList.add('d-none'));
        selectedShops.clear();
        updateSelectedCount();
    }
}

function toggleAllShops() {
    const checkboxes = document.querySelectorAll('.shop-checkbox');
    const isChecked = this.checked;

    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        if (isChecked) {
            selectedShops.add(checkbox.value);
        } else {
            selectedShops.delete(checkbox.value);
        }
    });

    updateSelectedCount();
}

function updateSelectedShops() {
    if (this.checked) {
        selectedShops.add(this.value);
    } else {
        selectedShops.delete(this.value);
    }
    updateSelectedCount();
}

function updateSelectedCount() {
    document.getElementById('selectedCount').textContent = `${selectedShops.size} selected`;
    document.getElementById('bulk_selected_count').textContent = selectedShops.size;
}

function showBulkCommissionModal() {
    if (selectedShops.size === 0) {
        showAlert('Please select shops first.', 'warning');
        return;
    }

    document.getElementById('bulk_shop_ids').value = Array.from(selectedShops).join(',');
    new bootstrap.Modal(document.getElementById('bulkCommissionModal')).show();
}

// AJAX functions
function loadAdminNotes(shopId) {
    fetch(`shops.php?ajax=admin_notes&shop_id=${shopId}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('existing_notes');
            if (data.length === 0) {
                container.innerHTML = '<div class="text-muted">No notes found.</div>';
            } else {
                container.innerHTML = data.map(note => `
                    <div class="border-bottom pb-2 mb-2">
                        <div class="small text-muted">${note.created_at} by ${note.admin_name}</div>
                        <div>${note.note}</div>
                    </div>
                `).join('');
            }
        })
        .catch(error => {
            console.error('Error loading notes:', error);
            document.getElementById('existing_notes').innerHTML = '<div class="text-danger">Error loading notes.</div>';
        });
}

function loadShopAnalytics(shopId) {
    fetch(`shops.php?ajax=shop_stats&shop_id=${shopId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('analytics_shop_name').textContent = data.shop.name;
            renderAnalytics(data);
        })
        .catch(error => {
            console.error('Error loading analytics:', error);
            document.getElementById('analytics_content').innerHTML = '<div class="text-danger">Error loading analytics.</div>';
        });
}

function renderAnalytics(data) {
    const content = document.getElementById('analytics_content');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">${data.orders.total_orders}</h5>
                        <p class="card-text">Total Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">৳${parseFloat(data.orders.total_revenue).toLocaleString()}</h5>
                        <p class="card-text">Total Revenue</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">৳${parseFloat(data.orders.avg_order_value).toLocaleString()}</h5>
                        <p class="card-text">Avg Order Value</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">${data.orders.completed_orders}</h5>
                        <p class="card-text">Completed Orders</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Utility functions
function submitAction(action, data) {
    showLoading(true);

    const formData = new FormData();
    formData.append('action', action);
    formData.append('ajax', '1');

    Object.keys(data).forEach(key => {
        formData.append(key, data[key]);
    });

    fetch('shops.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showLoading(false);
        console.error('Error:', error);
        showAlert('An error occurred. Please try again.', 'danger');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);
    setTimeout(() => alertDiv.remove(), 5000);
}

function showLoading(show) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

function refreshDashboard() {
    location.reload();
}

function changeLimit(newLimit) {
    const url = new URL(window.location);
    url.searchParams.set('limit', newLimit);
    url.searchParams.set('page', '1');
    window.location = url;
}

function exportShopsData() {
    window.open('export_shops.php', '_blank');
}

function printShopsList() {
    window.print();
}

function refreshTable() {
    location.reload();
}

// Withdrawal Request Management Functions
let selectedWithdrawals = new Set();
let currentWithdrawalPage = 1;
let withdrawalFilters = {};

function loadWithdrawalRequests(page = 1, filters = {}) {
    currentWithdrawalPage = page;
    withdrawalFilters = filters;

    const params = new URLSearchParams({
        ajax: 'withdrawal_requests',
        page: page,
        limit: 10,
        ...filters
    });

    fetch(`shops.php?${params}`)
        .then(response => response.json())
        .then(data => {
            renderWithdrawalRequests(data.requests);
            renderWithdrawalPagination(data);
        })
        .catch(error => {
            console.error('Error loading withdrawal requests:', error);
            document.getElementById('withdrawalRequestsBody').innerHTML =
                '<tr><td colspan="8" class="text-center text-danger">Error loading requests</td></tr>';
        });
}

function renderWithdrawalRequests(requests) {
    const tbody = document.getElementById('withdrawalRequestsBody');

    if (requests.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No withdrawal requests found</td></tr>';
        return;
    }

    tbody.innerHTML = requests.map(request => {
        const statusClass = getStatusClass(request.status);
        const methodIcon = getPaymentMethodIcon(request.payment_method);

        return `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input withdrawal-checkbox"
                           value="${request.id}" onchange="updateSelectedWithdrawals()">
                </td>
                <td>
                    <strong>#${request.id}</strong>
                    <div class="small text-muted">${formatDate(request.created_at)}</div>
                </td>
                <td>
                    <div class="fw-semibold">${escapeHtml(request.shop_name)}</div>
                    <div class="small text-muted">${escapeHtml(request.owner_name)}</div>
                </td>
                <td>
                    <div class="fw-bold">৳${parseFloat(request.request_amount).toLocaleString()}</div>
                    ${request.processing_fee > 0 ? `<div class="small text-muted">Fee: ৳${parseFloat(request.processing_fee).toLocaleString()}</div>` : ''}
                    ${request.final_amount ? `<div class="small text-success">Final: ৳${parseFloat(request.final_amount).toLocaleString()}</div>` : ''}
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="${methodIcon} me-2"></i>
                        ${request.payment_method_name || request.payment_method}
                    </div>
                    <div class="small text-muted">${request.account_type} - ${request.account_number}</div>
                </td>
                <td>
                    <span class="badge ${statusClass}">${capitalizeFirst(request.status)}</span>
                    ${request.admin_username ? `<div class="small text-muted">by ${request.admin_username}</div>` : ''}
                </td>
                <td>
                    <div class="small">${formatDate(request.created_at)}</div>
                    ${request.processed_at ? `<div class="small text-muted">Processed: ${formatDate(request.processed_at)}</div>` : ''}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                data-bs-toggle="dropdown">
                            Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="#" onclick="showWithdrawalDetails(${request.id})">
                                    <i class="fas fa-eye me-2"></i>View Details
                                </a>
                            </li>
                            ${request.status === 'pending' || request.status === 'processing' ? `
                                <li>
                                    <a class="dropdown-item" href="#" onclick="updateWithdrawalStatus(${request.id})">
                                        <i class="fas fa-edit me-2"></i>Update Status
                                    </a>
                                </li>
                            ` : ''}
                            ${request.status === 'completed' && request.transaction_reference ? `
                                <li>
                                    <a class="dropdown-item" href="#" onclick="copyTransactionRef('${request.transaction_reference}')">
                                        <i class="fas fa-copy me-2"></i>Copy Transaction Ref
                                    </a>
                                </li>
                            ` : ''}
                        </ul>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function renderWithdrawalPagination(data) {
    const pagination = document.getElementById('withdrawalPagination');

    if (data.total_pages <= 1) {
        pagination.classList.add('d-none');
        return;
    }

    pagination.classList.remove('d-none');
    const ul = pagination.querySelector('ul');

    let paginationHTML = '';

    // Previous button
    if (data.page > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadWithdrawalRequests(${data.page - 1}, withdrawalFilters)">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
    }

    // Page numbers
    const startPage = Math.max(1, data.page - 2);
    const endPage = Math.min(data.total_pages, data.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === data.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadWithdrawalRequests(${i}, withdrawalFilters)">${i}</a>
            </li>
        `;
    }

    // Next button
    if (data.page < data.total_pages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadWithdrawalRequests(${data.page + 1}, withdrawalFilters)">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    }

    ul.innerHTML = paginationHTML;
}

function loadWithdrawalStats() {
    fetch('shops.php?ajax=withdrawal_stats')
        .then(response => response.json())
        .then(stats => {
            document.getElementById('processing_count').textContent = stats.processing_requests || 0;
            document.getElementById('rejected_count').textContent = stats.rejected_requests || 0;
        })
        .catch(error => console.error('Error loading withdrawal stats:', error));
}

function showWithdrawalFilters() {
    const filtersCard = document.getElementById('withdrawalFiltersCard');
    filtersCard.classList.toggle('d-none');
}

function updateWithdrawalStatus(requestId) {
    fetch(`shops.php?ajax=withdrawal_requests&request_id=${requestId}`)
        .then(response => response.json())
        .then(data => {
            if (data.requests && data.requests.length > 0) {
                const request = data.requests[0];

                document.getElementById('withdrawal_request_id').value = request.id;
                document.getElementById('withdrawal_request_display_id').textContent = request.id;
                document.getElementById('withdrawal_shop_name').textContent = request.shop_name;
                document.getElementById('withdrawal_amount').textContent = parseFloat(request.request_amount).toLocaleString();
                document.getElementById('withdrawal_current_status').textContent = capitalizeFirst(request.status);

                // Reset form
                document.getElementById('updateWithdrawalStatusForm').reset();
                document.getElementById('withdrawal_request_id').value = request.id;

                new bootstrap.Modal(document.getElementById('updateWithdrawalStatusModal')).show();
            }
        })
        .catch(error => {
            console.error('Error loading request details:', error);
            showAlert('Error loading request details', 'danger');
        });
}

function showWithdrawalDetails(requestId) {
    document.getElementById('withdrawal_details_content').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading request details...</p>
        </div>
    `;

    new bootstrap.Modal(document.getElementById('withdrawalDetailsModal')).show();

    // Load detailed request information
    fetch(`shops.php?ajax=withdrawal_requests&request_id=${requestId}`)
        .then(response => response.json())
        .then(data => {
            if (data.requests && data.requests.length > 0) {
                renderWithdrawalDetails(data.requests[0]);
            }
        })
        .catch(error => {
            console.error('Error loading request details:', error);
            document.getElementById('withdrawal_details_content').innerHTML =
                '<div class="text-center text-danger">Error loading request details</div>';
        });
}

function renderWithdrawalDetails(request) {
    const statusClass = getStatusClass(request.status);
    const methodIcon = getPaymentMethodIcon(request.payment_method);

    document.getElementById('withdrawal_details_content').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Request Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Request ID:</strong></td><td>#${request.id}</td></tr>
                    <tr><td><strong>Amount:</strong></td><td>৳${parseFloat(request.request_amount).toLocaleString()}</td></tr>
                    <tr><td><strong>Processing Fee:</strong></td><td>৳${parseFloat(request.processing_fee || 0).toLocaleString()}</td></tr>
                    <tr><td><strong>Final Amount:</strong></td><td>৳${parseFloat(request.final_amount || request.request_amount).toLocaleString()}</td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${statusClass}">${capitalizeFirst(request.status)}</span></td></tr>
                    <tr><td><strong>Created:</strong></td><td>${formatDateTime(request.created_at)}</td></tr>
                    ${request.processed_at ? `<tr><td><strong>Processed:</strong></td><td>${formatDateTime(request.processed_at)}</td></tr>` : ''}
                    ${request.completed_at ? `<tr><td><strong>Completed:</strong></td><td>${formatDateTime(request.completed_at)}</td></tr>` : ''}
                </table>
            </div>
            <div class="col-md-6">
                <h6>Shop & Payment Details</h6>
                <table class="table table-sm">
                    <tr><td><strong>Shop:</strong></td><td>${escapeHtml(request.shop_name)}</td></tr>
                    <tr><td><strong>Owner:</strong></td><td>${escapeHtml(request.owner_name)}</td></tr>
                    <tr><td><strong>Payment Method:</strong></td><td><i class="${methodIcon} me-2"></i>${request.payment_method_name || request.payment_method}</td></tr>
                    <tr><td><strong>Account Type:</strong></td><td>${capitalizeFirst(request.account_type)}</td></tr>
                    <tr><td><strong>Account Number:</strong></td><td>${request.account_number}</td></tr>
                    <tr><td><strong>Account Holder:</strong></td><td>${escapeHtml(request.account_holder_name)}</td></tr>
                    ${request.transaction_reference ? `<tr><td><strong>Transaction Ref:</strong></td><td>${escapeHtml(request.transaction_reference)}</td></tr>` : ''}
                </table>
            </div>
        </div>

        ${request.shop_owner_notes ? `
            <div class="mt-3">
                <h6>Shop Owner Notes</h6>
                <div class="alert alert-light">${escapeHtml(request.shop_owner_notes)}</div>
            </div>
        ` : ''}

        ${request.admin_notes ? `
            <div class="mt-3">
                <h6>Admin Notes</h6>
                <div class="alert alert-info">${escapeHtml(request.admin_notes)}</div>
            </div>
        ` : ''}

        ${request.rejection_reason ? `
            <div class="mt-3">
                <h6>Rejection Reason</h6>
                <div class="alert alert-danger">${escapeHtml(request.rejection_reason)}</div>
            </div>
        ` : ''}
    `;
}

function updateSelectedWithdrawals() {
    const checkboxes = document.querySelectorAll('.withdrawal-checkbox:checked');
    selectedWithdrawals.clear();

    checkboxes.forEach(checkbox => {
        selectedWithdrawals.add(checkbox.value);
    });

    // Update bulk action button visibility
    const bulkActions = document.querySelector('.withdrawal-bulk-actions');
    if (bulkActions) {
        bulkActions.style.display = selectedWithdrawals.size > 0 ? 'block' : 'none';
    }
}

function refreshWithdrawalRequests() {
    loadWithdrawalRequests(currentWithdrawalPage, withdrawalFilters);
    loadWithdrawalStats();
}

// Utility functions for withdrawal requests
function getStatusClass(status) {
    const statusClasses = {
        'pending': 'bg-warning text-dark',
        'processing': 'bg-info text-white',
        'approved': 'bg-primary text-white',
        'completed': 'bg-success text-white',
        'rejected': 'bg-danger text-white',
        'cancelled': 'bg-secondary text-white'
    };
    return statusClasses[status] || 'bg-secondary text-white';
}

function getPaymentMethodIcon(method) {
    const icons = {
        'bkash': 'fas fa-mobile-alt text-danger',
        'nagad': 'fas fa-mobile-alt text-warning',
        'rocket': 'fas fa-mobile-alt text-info',
        'bank_transfer': 'fas fa-university text-primary'
    };
    return icons[method] || 'fas fa-credit-card';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function copyTransactionRef(ref) {
    navigator.clipboard.writeText(ref).then(() => {
        showAlert('Transaction reference copied to clipboard', 'success');
    }).catch(() => {
        showAlert('Failed to copy transaction reference', 'danger');
    });
}

function printWithdrawalDetails() {
    const content = document.getElementById('withdrawal_details_content').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Withdrawal Request Details</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            </head>
            <body class="p-4">
                <h3>Withdrawal Request Details</h3>
                ${content}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Event listeners for withdrawal forms
document.getElementById('withdrawalFiltersForm')?.addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const filters = Object.fromEntries(formData.entries());
    loadWithdrawalRequests(1, filters);
});

document.getElementById('updateWithdrawalStatusForm')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('ajax', '1');

    showLoading(true);

    fetch('shops.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('updateWithdrawalStatusModal')).hide();
            refreshWithdrawalRequests();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showLoading(false);
        console.error('Error:', error);
        showAlert('An error occurred while updating status', 'danger');
    });
});

// Status change handler for withdrawal modal
document.getElementById('new_status')?.addEventListener('change', function() {
    const status = this.value;
    const transactionField = document.getElementById('transaction_reference_field');
    const rejectionField = document.getElementById('rejection_reason_field');

    // Show/hide fields based on status
    if (status === 'approved' || status === 'completed') {
        transactionField.style.display = 'block';
        rejectionField.style.display = 'none';
        document.getElementById('transaction_reference').required = true;
        document.getElementById('rejection_reason').required = false;
    } else if (status === 'rejected') {
        transactionField.style.display = 'none';
        rejectionField.style.display = 'block';
        document.getElementById('transaction_reference').required = false;
        document.getElementById('rejection_reason').required = true;
    } else {
        transactionField.style.display = 'none';
        rejectionField.style.display = 'none';
        document.getElementById('transaction_reference').required = false;
        document.getElementById('rejection_reason').required = false;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
