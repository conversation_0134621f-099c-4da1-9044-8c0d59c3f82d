<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="8dp">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="150dp"
        android:layout_height="100dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp"
        android:background="@drawable/glass_icon_background">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/glass_section_background">

            <ImageView
                android:id="@+id/service_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="12dp"
                android:contentDescription="@string/service_icon"
                android:scaleType="centerInside"
                tools:src="@drawable/ic_washing_machine" />

        </FrameLayout>
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/service_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:textSize="16sp"
        android:textColor="@color/bottom_nav_icon_tint"
        android:textStyle="bold"
        android:background="@drawable/glass_section_background"
        android:padding="6dp"
        android:elevation="2dp"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="Dry Cleaning" />

    <TextView
        android:id="@+id/service_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:textSize="10sp"
        android:textColor="@color/text_secondary"
        android:visibility="gone"
        tools:text="" />

</LinearLayout>
