-- GoGoLaundry Shop System Database Migration
-- This file contains all the database changes needed for the shop discovery feature

-- 1. Create laundry_shops table
CREATE TABLE IF NOT EXISTS `laundry_shops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `bn_name` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `bn_description` text DEFAULT NULL,
  `owner_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) NOT NULL,
  `division_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `upazilla_id` int(11) DEFAULT NULL,
  `latitude` decimal(10,8) NOT NULL,
  `longitude` decimal(11,8) NOT NULL,
  `operating_hours` json DEFAULT NULL,
  `rating` decimal(3,2) DEFAULT 0.00,
  `total_reviews` int(11) DEFAULT 0,
  `commission_percentage` decimal(5,2) DEFAULT 10.00,
  `is_active` tinyint(1) DEFAULT 1,
  `is_verified` tinyint(1) DEFAULT 0,
  `profile_image_url` varchar(255) DEFAULT NULL,
  `cover_image_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `email` (`email`),
  KEY `location_idx` (`latitude`,`longitude`),
  KEY `division_district_upazilla` (`division_id`,`district_id`,`upazilla_id`),
  KEY `rating_idx` (`rating`,`total_reviews`),
  KEY `active_verified_idx` (`is_active`,`is_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create shop_services table
CREATE TABLE IF NOT EXISTS `shop_services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `is_available` tinyint(1) DEFAULT 1,
  `estimated_hours` int(11) DEFAULT 24,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `shop_service_unique` (`shop_id`,`service_id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `service_id_idx` (`service_id`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`service_id`) REFERENCES `services`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Create shop_items table
CREATE TABLE IF NOT EXISTS `shop_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `custom_price` decimal(10,2) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT 1,
  `estimated_hours` int(11) DEFAULT 24,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `shop_item_unique` (`shop_id`,`item_id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `item_id_idx` (`item_id`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Create shop_reviews table
CREATE TABLE IF NOT EXISTS `shop_reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `rating` tinyint(1) NOT NULL CHECK (`rating` >= 1 AND `rating` <= 5),
  `review_text` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_shop_review` (`user_id`,`shop_id`,`order_id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `user_id_idx` (`user_id`),
  KEY `rating_idx` (`rating`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Create delivery_zones table
CREATE TABLE IF NOT EXISTS `delivery_zones` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `division_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `upazilla_id` int(11) DEFAULT NULL,
  `delivery_fee` decimal(10,2) DEFAULT 0.00,
  `min_order_amount` decimal(10,2) DEFAULT 0.00,
  `estimated_delivery_hours` int(11) DEFAULT 48,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `location_idx` (`division_id`,`district_id`,`upazilla_id`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Create commission_transactions table
CREATE TABLE IF NOT EXISTS `commission_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `shop_id` int(11) NOT NULL,
  `order_subtotal` decimal(10,2) NOT NULL,
  `commission_percentage` decimal(5,2) NOT NULL,
  `base_commission` decimal(10,2) NOT NULL,
  `adjusted_commission` decimal(10,2) NOT NULL,
  `shop_earnings` decimal(10,2) NOT NULL,
  `platform_earnings` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `paid_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_commission_unique` (`order_id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `status_idx` (`status`),
  KEY `created_at_idx` (`created_at`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. Update orders table to include shop information
ALTER TABLE `orders`
ADD COLUMN IF NOT EXISTS `shop_id` int(11) DEFAULT NULL AFTER `user_id`,
ADD COLUMN IF NOT EXISTS `fulfillment_type` enum('pickup_delivery','manual_dropoff') DEFAULT 'pickup_delivery' AFTER `shop_id`,
ADD COLUMN IF NOT EXISTS `shop_commission` decimal(10,2) DEFAULT 0.00 AFTER `fulfillment_type`;

-- Add foreign key for shop_id if it doesn't exist
SET @fk_exists = (SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE
                  WHERE TABLE_SCHEMA = DATABASE()
                  AND TABLE_NAME = 'orders'
                  AND CONSTRAINT_NAME = 'orders_shop_fk');

SET @sql = IF(@fk_exists = 0,
    'ALTER TABLE orders ADD CONSTRAINT orders_shop_fk FOREIGN KEY (shop_id) REFERENCES laundry_shops(id)',
    'SELECT "Foreign key already exists"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. Create shop_owners table for authentication
CREATE TABLE IF NOT EXISTS `shop_owners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(15) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `shop_id` (`shop_id`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `email` (`email`),
  KEY `active_verified_idx` (`is_active`,`is_verified`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 9. Insert sample laundry shops data
INSERT IGNORE INTO `laundry_shops` (`id`, `name`, `bn_name`, `description`, `owner_name`, `phone`, `email`, `address`, `division_id`, `district_id`, `upazilla_id`, `latitude`, `longitude`, `operating_hours`, `rating`, `total_reviews`, `commission_percentage`, `is_active`, `is_verified`) VALUES
(1, 'Clean & Fresh Laundry', 'ক্লিন এন্ড ফ্রেশ লন্ড্রি', 'Professional laundry service with modern equipment and eco-friendly cleaning solutions.', 'Mohammad Rahman', '01712345678', '<EMAIL>', 'House 123, Road 15, Dhanmondi, Dhaka', 1, 1, 1, 23.7461, 90.3742, '{"monday":{"is_open":true,"open_time":"08:00","close_time":"22:00"},"tuesday":{"is_open":true,"open_time":"08:00","close_time":"22:00"},"wednesday":{"is_open":true,"open_time":"08:00","close_time":"22:00"},"thursday":{"is_open":true,"open_time":"08:00","close_time":"22:00"},"friday":{"is_open":true,"open_time":"08:00","close_time":"22:00"},"saturday":{"is_open":true,"open_time":"08:00","close_time":"22:00"},"sunday":{"is_open":true,"open_time":"10:00","close_time":"20:00"}}', 4.5, 123, 12.00, 1, 1),
(2, 'Express Wash Center', 'এক্সপ্রেস ওয়াশ সেন্টার', 'Fast and reliable laundry service with same-day delivery options.', 'Fatima Khatun', '01823456789', '<EMAIL>', 'Plot 45, Gulshan Avenue, Gulshan, Dhaka', 1, 1, 2, 23.7925, 90.4078, '{"monday":{"is_open":true,"open_time":"07:00","close_time":"23:00"},"tuesday":{"is_open":true,"open_time":"07:00","close_time":"23:00"},"wednesday":{"is_open":true,"open_time":"07:00","close_time":"23:00"},"thursday":{"is_open":true,"open_time":"07:00","close_time":"23:00"},"friday":{"is_open":true,"open_time":"07:00","close_time":"23:00"},"saturday":{"is_open":true,"open_time":"07:00","close_time":"23:00"},"sunday":{"is_open":true,"open_time":"09:00","close_time":"21:00"}}', 4.2, 87, 10.00, 1, 1),
(3, 'Premium Dry Cleaners', 'প্রিমিয়াম ড্রাই ক্লিনার্স', 'Specialized in dry cleaning and premium garment care services.', 'Ahmed Hassan', '01934567890', '<EMAIL>', 'Building 78, Banani Commercial Area, Dhaka', 1, 1, 3, 23.7937, 90.4066, '{"monday":{"is_open":true,"open_time":"09:00","close_time":"21:00"},"tuesday":{"is_open":true,"open_time":"09:00","close_time":"21:00"},"wednesday":{"is_open":true,"open_time":"09:00","close_time":"21:00"},"thursday":{"is_open":true,"open_time":"09:00","close_time":"21:00"},"friday":{"is_open":true,"open_time":"09:00","close_time":"21:00"},"saturday":{"is_open":true,"open_time":"09:00","close_time":"21:00"},"sunday":{"is_open":false,"open_time":"","close_time":""}}', 4.8, 156, 15.00, 1, 1);

-- 9. Insert sample shop services
INSERT IGNORE INTO `shop_services` (`shop_id`, `service_id`, `is_available`, `estimated_hours`) VALUES
(1, 1, 1, 24), -- Clean & Fresh - Wash & Fold
(1, 2, 1, 48), -- Clean & Fresh - Dry Cleaning
(1, 3, 1, 12), -- Clean & Fresh - Ironing
(2, 1, 1, 12), -- Express Wash - Wash & Fold
(2, 3, 1, 6),  -- Express Wash - Ironing
(3, 2, 1, 24), -- Premium Dry - Dry Cleaning
(3, 3, 1, 24); -- Premium Dry - Ironing

-- 10. Insert sample shop items (assuming items exist)
INSERT IGNORE INTO `shop_items` (`shop_id`, `item_id`, `custom_price`, `is_available`, `estimated_hours`) VALUES
(1, 1, NULL, 1, 24),    -- Clean & Fresh - Men's Shirt (default price)
(1, 2, 55.00, 1, 24),   -- Clean & Fresh - Women's Blouse (custom price)
(1, 3, NULL, 1, 48),    -- Clean & Fresh - Pants (default price)
(2, 1, 35.00, 1, 12),   -- Express Wash - Men's Shirt (custom price)
(2, 2, NULL, 1, 12),    -- Express Wash - Women's Blouse (default price)
(3, 3, 120.00, 1, 24),  -- Premium Dry - Pants (custom price)
(3, 4, NULL, 1, 48);    -- Premium Dry - Suit (default price)

-- 11. Insert sample delivery zones
INSERT IGNORE INTO `delivery_zones` (`shop_id`, `division_id`, `district_id`, `upazilla_id`, `delivery_fee`, `min_order_amount`, `estimated_delivery_hours`) VALUES
(1, 1, 1, 1, 50.00, 200.00, 24),  -- Clean & Fresh - Dhanmondi
(1, 1, 1, 2, 80.00, 300.00, 48),  -- Clean & Fresh - Gulshan
(2, 1, 1, 2, 30.00, 150.00, 12),  -- Express Wash - Gulshan
(2, 1, 1, 3, 60.00, 250.00, 24),  -- Express Wash - Banani
(3, 1, 1, 3, 40.00, 500.00, 24);  -- Premium Dry - Banani

-- 12. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shops_location ON laundry_shops(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_shops_rating ON laundry_shops(rating DESC, total_reviews DESC);
CREATE INDEX IF NOT EXISTS idx_commission_shop_date ON commission_transactions(shop_id, created_at);
CREATE INDEX IF NOT EXISTS idx_orders_shop ON orders(shop_id);

-- 13. Create triggers to update shop ratings
DELIMITER //

CREATE TRIGGER IF NOT EXISTS update_shop_rating_after_review_insert
AFTER INSERT ON shop_reviews
FOR EACH ROW
BEGIN
    UPDATE laundry_shops
    SET
        rating = (
            SELECT AVG(rating)
            FROM shop_reviews
            WHERE shop_id = NEW.shop_id
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM shop_reviews
            WHERE shop_id = NEW.shop_id
        )
    WHERE id = NEW.shop_id;
END//

CREATE TRIGGER IF NOT EXISTS update_shop_rating_after_review_update
AFTER UPDATE ON shop_reviews
FOR EACH ROW
BEGIN
    UPDATE laundry_shops
    SET
        rating = (
            SELECT AVG(rating)
            FROM shop_reviews
            WHERE shop_id = NEW.shop_id
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM shop_reviews
            WHERE shop_id = NEW.shop_id
        )
    WHERE id = NEW.shop_id;
END//

CREATE TRIGGER IF NOT EXISTS update_shop_rating_after_review_delete
AFTER DELETE ON shop_reviews
FOR EACH ROW
BEGIN
    UPDATE laundry_shops
    SET
        rating = COALESCE((
            SELECT AVG(rating)
            FROM shop_reviews
            WHERE shop_id = OLD.shop_id
        ), 0),
        total_reviews = (
            SELECT COUNT(*)
            FROM shop_reviews
            WHERE shop_id = OLD.shop_id
        )
    WHERE id = OLD.shop_id;
END//

DELIMITER ;

-- Migration completed successfully
SELECT 'GoGoLaundry Shop System Migration Completed Successfully!' as status;
