<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="180dp"
    android:background="#6E7DE0"
    android:padding="12dp"
    android:theme="@style/ThemeOverlay.MaterialComponents.Dark">

    <!-- Glass container for profile image -->
    <FrameLayout
        android:id="@+id/profile_image_container"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/glass_card_background"
        android:padding="2dp"
        app:layout_constraintEnd_toStartOf="@+id/user_info_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/profile_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="2dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            app:strokeColor="@android:color/white"
            app:strokeWidth="2dp" />

        <ImageView
            android:id="@+id/edit_profile_image"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="0dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/glass_card_background"
            android:elevation="4dp"
            android:padding="3dp"
            android:src="@drawable/ic_edit"
            app:tint="@android:color/white" />

    </FrameLayout>

    <!-- User information container -->
    <LinearLayout
        android:id="@+id/user_info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="4dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/profile_image_container"
        app:layout_constraintTop_toTopOf="@id/profile_image_container">

        <TextView
            android:id="@+id/nav_header_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="User Name"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/nav_header_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="+8801XXXXXXXXX"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#E0FFFFFF"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/nav_header_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="<EMAIL>"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#C0FFFFFF"
            android:textSize="11sp" />

    </LinearLayout>

    <!-- Decorative glass elements -->
    <View
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="12dp"
        android:alpha="0.3"
        android:background="@drawable/glass_card_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="6dp"
        android:alpha="0.2"
        android:background="@drawable/glass_card_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
