-- Merging decision tree log ---
manifest
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:2:1-102:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ae4eed1b1ae105f01d3e7330e2833b6\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf2455e731a9e5bf1a1240e4ec32ba6d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3701360ffe31444e45a8a1f5dba73686\transformed\PhotoView-2.3.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d26dc28bd39f61dbdf808883d97a6d3c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b1a457fa5d970673514889d79cab362f\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\296d57ea5b86e141bead38b6150ec5bd\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\45823f9be481063fd3d75aa15b23a5cb\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311321c91ea507f452df293839717be\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f716dfc83cb8e5033ee3eacd43859f9\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca7c814e52a4f5deff589f4db75cdd05\transformed\play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e8285f0861fbac70667304201617486\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-core:21.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea998eec7d211504a34783e01607d357\transformed\firebase-core-21.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82f74204f4fb60e55b82ff1e5cec1e90\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fcb2c87290a1ff0e993ab82af10ba16e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d0eec9dcd205fd3fbd971ba0265549ac\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29ebecdaae29c676f11c2990046d6dbf\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f21fc4b8395428f536146a241d4c5a6\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f0b68f9747c71f02df78125a6cf3c50\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82025675902f6a85320bf7643201acf1\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e09fb5e37152267e50e5306bc9c0c86d\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c14bce25d5af8b3baeca7cd08a271cdb\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\00596551c31d248811f1b66873b9f7d4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfe645f5abe7e16c9eebab287fc0d954\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e546e96a375d1d88b2a082db553eacb\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4b784b384573183809d6aacf279c0d79\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d4f8809fb4c159f06f2b92383612fd5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21428ceb6758f507d5e4fd7569ce4ad3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06971c9ac4b7b6c4cb69431a220471d1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c0614750c3b7e887165d0dcf53200ee2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee33c29cd185ec66234cbfa18ac26f74\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1cb7a81596308995479078f8cebc33a1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ffc1f2a80960a680f82f6ab424275895\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0cdf07af54fa0225cfdcf46194a6ca8b\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be59f2a5b3dd115371e7b6a1346b6da9\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cd2071e74c93d791934067b47349a6e9\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\852e3778790adcb308eb374d5f425fcf\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\25ec63c15b5d0d9d2f52abeb23cb0f1c\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9d6b4bd586497d8340d1f4ae3e410b4\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\70c4037fd6211ff6e6948935008de250\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aeb4906669dd9c6d248cabbf1b3eb1a6\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6a78ff72fff7d84d1adf5a6a5231c60\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b75e1241c96057b3f2ecc7fc622170eb\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e803a21e40f4f47a7be5edc22c63580a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dccc60a5514491a962386039c53ad067\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4eba426a7fa4dbb5ffe1c86c40633694\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e68673ff0a29529606855b32bb45cd3c\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c31bb31d0c0a9ca9efeb309970d98416\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d33f8fcb8e6ec6de69937c85231a8678\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4edb4fa237ded69772f2a19f3222541d\transformed\library-1.6.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba59ae180d1651210b3c30b2387827ce\transformed\shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.osmdroid:osmdroid-mapsforge:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\11993136c7a0cccc2ba2279295fd7c65\transformed\osmdroid-mapsforge-6.1.17\AndroidManifest.xml:2:1-9:12
MERGED from [org.osmdroid:osmdroid-wms:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f6bb62380b6add8ae46e6e0b8a0f5737\transformed\osmdroid-wms-6.1.17\AndroidManifest.xml:2:1-9:12
MERGED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:2:1-27:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efc2418746f9fe3f39d87378b1267f70\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdba10c34d5ade66353f7678cb2763fc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8d2920a6edc1b5810fd0bd7b3f1c35f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\004bc35284e5763afe11914b858aa4be\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2fba1b995cf1d08f2e1291ba5fbeeef\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1edd40a93e9f035a927ca216bcae34b0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\113ae1c341d60d0ff34f97098af5c016\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90ba95ff2dc6a4c83c2f4b7f3b902254\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\519ba7962a5760299625d441daaaeea3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\253687ec00bfffdc57e79b7379677ac6\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e4acdb412f744a2dc37ff0dd9477f43e\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b3a2f8e3db158eb1ad0e1657b32b52f\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d7d83b64d29677e20bc3a10f58e24a88\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e59e46cd690368fa6edf4bbaba71f687\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c4c1618eb7fc9368656b131cf88c490\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\60861d183da333a5e3e50b62b1bd06fe\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e8a622068c0edc7ae7492e5f95b8d5df\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e9bc98679171a20e588e1fb7c8cf1c2\transformed\materialish-progress-1.7\AndroidManifest.xml:2:1-13:12
	package
		INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.camera
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:6:5-8:36
	android:required
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:8:9-33
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:7:9-47
uses-permission#android.permission.INTERNET
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:10:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:11:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:11:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:12:5-107
	android:maxSdkVersion
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:12:78-104
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:5-108
	android:maxSdkVersion
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:79-105
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:13:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:14:5-76
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:14:22-73
uses-permission#android.permission.CAMERA
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:15:5-65
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:15:22-62
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:16:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:16:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:17:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:17:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:18:5-66
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:18:22-63
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:21:5-79
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:21:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:22:5-81
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:22:22-78
application
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:29:5-100:19
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:29:5-100:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ae4eed1b1ae105f01d3e7330e2833b6\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ae4eed1b1ae105f01d3e7330e2833b6\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf2455e731a9e5bf1a1240e4ec32ba6d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf2455e731a9e5bf1a1240e4ec32ba6d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\296d57ea5b86e141bead38b6150ec5bd\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\296d57ea5b86e141bead38b6150ec5bd\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca7c814e52a4f5deff589f4db75cdd05\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca7c814e52a4f5deff589f4db75cdd05\transformed\play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e8285f0861fbac70667304201617486\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e8285f0861fbac70667304201617486\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-core:21.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea998eec7d211504a34783e01607d357\transformed\firebase-core-21.1.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-core:21.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea998eec7d211504a34783e01607d357\transformed\firebase-core-21.1.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82f74204f4fb60e55b82ff1e5cec1e90\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82f74204f4fb60e55b82ff1e5cec1e90\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fcb2c87290a1ff0e993ab82af10ba16e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fcb2c87290a1ff0e993ab82af10ba16e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d0eec9dcd205fd3fbd971ba0265549ac\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d0eec9dcd205fd3fbd971ba0265549ac\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29ebecdaae29c676f11c2990046d6dbf\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29ebecdaae29c676f11c2990046d6dbf\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dccc60a5514491a962386039c53ad067\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dccc60a5514491a962386039c53ad067\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4eba426a7fa4dbb5ffe1c86c40633694\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4eba426a7fa4dbb5ffe1c86c40633694\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4edb4fa237ded69772f2a19f3222541d\transformed\library-1.6.2\AndroidManifest.xml:11:5-20
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4edb4fa237ded69772f2a19f3222541d\transformed\library-1.6.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2fba1b995cf1d08f2e1291ba5fbeeef\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2fba1b995cf1d08f2e1291ba5fbeeef\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e9bc98679171a20e588e1fb7c8cf1c2\transformed\materialish-progress-1.7\AndroidManifest.xml:11:5-20
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e9bc98679171a20e588e1fb7c8cf1c2\transformed\materialish-progress-1.7\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:37:9-54
	android:configChanges
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:40:9-83
	android:icon
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:35:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:36:9-41
	android:fullBackupContent
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:34:9-54
	tools:targetApi
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:41:9-29
	android:allowBackup
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:39:9-49
	android:dataExtractionRules
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:33:9-65
	android:usesCleartextTraffic
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:31:9-44
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:30:9-39
activity#com.mdsadrulhasan.gogolaundry.LoginActivity
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:44:9-51:20
	android:exported
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:46:13-36
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:45:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:47:13-50:29
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:48:17-69
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:48:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:49:17-77
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:49:27-74
activity#com.mdsadrulhasan.gogolaundry.SignupActivity
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:54:9-56:40
	android:exported
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:55:13-43
activity#com.mdsadrulhasan.gogolaundry.ForgotPasswordActivity
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:59:9-61:40
	android:exported
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:60:13-51
activity#com.mdsadrulhasan.gogolaundry.MainActivity
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:64:9-67:90
	android:exported
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:66:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:67:13-87
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:65:13-41
provider#androidx.core.content.FileProvider
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:71:9-79:20
	android:grantUriPermissions
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:75:13-47
	android:authorities
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:73:13-77
	android:exported
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:74:13-37
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:72:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:76:13-78:54
	android:resource
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:78:17-51
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:77:17-67
service#com.mdsadrulhasan.gogolaundry.fcm.GoGoLaundryFirebaseMessagingService
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:82:9-88:19
	android:exported
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:84:13-37
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:83:13-68
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:85:13-87:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:86:17-78
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:86:25-75
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:91:9-93:60
	android:resource
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:93:13-57
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:92:13-83
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:94:9-96:54
	android:resource
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:96:13-51
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:95:13-84
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:97:9-99:57
	android:value
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:99:13-54
	android:name
		ADDED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml:98:13-89
uses-sdk
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ae4eed1b1ae105f01d3e7330e2833b6\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6ae4eed1b1ae105f01d3e7330e2833b6\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf2455e731a9e5bf1a1240e4ec32ba6d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bf2455e731a9e5bf1a1240e4ec32ba6d\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3701360ffe31444e45a8a1f5dba73686\transformed\PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3701360ffe31444e45a8a1f5dba73686\transformed\PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d26dc28bd39f61dbdf808883d97a6d3c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d26dc28bd39f61dbdf808883d97a6d3c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b1a457fa5d970673514889d79cab362f\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b1a457fa5d970673514889d79cab362f\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\296d57ea5b86e141bead38b6150ec5bd\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\296d57ea5b86e141bead38b6150ec5bd\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\45823f9be481063fd3d75aa15b23a5cb\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\45823f9be481063fd3d75aa15b23a5cb\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311321c91ea507f452df293839717be\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e311321c91ea507f452df293839717be\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f716dfc83cb8e5033ee3eacd43859f9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0f716dfc83cb8e5033ee3eacd43859f9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca7c814e52a4f5deff589f4db75cdd05\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ca7c814e52a4f5deff589f4db75cdd05\transformed\play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e8285f0861fbac70667304201617486\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7e8285f0861fbac70667304201617486\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-core:21.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea998eec7d211504a34783e01607d357\transformed\firebase-core-21.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-core:21.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ea998eec7d211504a34783e01607d357\transformed\firebase-core-21.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82f74204f4fb60e55b82ff1e5cec1e90\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82f74204f4fb60e55b82ff1e5cec1e90\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fcb2c87290a1ff0e993ab82af10ba16e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\fcb2c87290a1ff0e993ab82af10ba16e\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d0eec9dcd205fd3fbd971ba0265549ac\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d0eec9dcd205fd3fbd971ba0265549ac\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29ebecdaae29c676f11c2990046d6dbf\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\29ebecdaae29c676f11c2990046d6dbf\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f21fc4b8395428f536146a241d4c5a6\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5f21fc4b8395428f536146a241d4c5a6\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f0b68f9747c71f02df78125a6cf3c50\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3f0b68f9747c71f02df78125a6cf3c50\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82025675902f6a85320bf7643201acf1\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\82025675902f6a85320bf7643201acf1\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e09fb5e37152267e50e5306bc9c0c86d\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e09fb5e37152267e50e5306bc9c0c86d\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c14bce25d5af8b3baeca7cd08a271cdb\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c14bce25d5af8b3baeca7cd08a271cdb\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\00596551c31d248811f1b66873b9f7d4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\00596551c31d248811f1b66873b9f7d4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfe645f5abe7e16c9eebab287fc0d954\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cfe645f5abe7e16c9eebab287fc0d954\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e546e96a375d1d88b2a082db553eacb\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0e546e96a375d1d88b2a082db553eacb\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4b784b384573183809d6aacf279c0d79\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4b784b384573183809d6aacf279c0d79\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d4f8809fb4c159f06f2b92383612fd5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1d4f8809fb4c159f06f2b92383612fd5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21428ceb6758f507d5e4fd7569ce4ad3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21428ceb6758f507d5e4fd7569ce4ad3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06971c9ac4b7b6c4cb69431a220471d1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\06971c9ac4b7b6c4cb69431a220471d1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c0614750c3b7e887165d0dcf53200ee2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c0614750c3b7e887165d0dcf53200ee2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee33c29cd185ec66234cbfa18ac26f74\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee33c29cd185ec66234cbfa18ac26f74\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1cb7a81596308995479078f8cebc33a1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1cb7a81596308995479078f8cebc33a1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ffc1f2a80960a680f82f6ab424275895\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ffc1f2a80960a680f82f6ab424275895\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0cdf07af54fa0225cfdcf46194a6ca8b\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0cdf07af54fa0225cfdcf46194a6ca8b\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be59f2a5b3dd115371e7b6a1346b6da9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\be59f2a5b3dd115371e7b6a1346b6da9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cd2071e74c93d791934067b47349a6e9\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cd2071e74c93d791934067b47349a6e9\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\852e3778790adcb308eb374d5f425fcf\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\852e3778790adcb308eb374d5f425fcf\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\25ec63c15b5d0d9d2f52abeb23cb0f1c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\25ec63c15b5d0d9d2f52abeb23cb0f1c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9d6b4bd586497d8340d1f4ae3e410b4\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c9d6b4bd586497d8340d1f4ae3e410b4\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\70c4037fd6211ff6e6948935008de250\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\70c4037fd6211ff6e6948935008de250\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aeb4906669dd9c6d248cabbf1b3eb1a6\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\aeb4906669dd9c6d248cabbf1b3eb1a6\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6a78ff72fff7d84d1adf5a6a5231c60\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a6a78ff72fff7d84d1adf5a6a5231c60\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b75e1241c96057b3f2ecc7fc622170eb\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b75e1241c96057b3f2ecc7fc622170eb\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e803a21e40f4f47a7be5edc22c63580a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e803a21e40f4f47a7be5edc22c63580a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dccc60a5514491a962386039c53ad067\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\dccc60a5514491a962386039c53ad067\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4eba426a7fa4dbb5ffe1c86c40633694\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4eba426a7fa4dbb5ffe1c86c40633694\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e68673ff0a29529606855b32bb45cd3c\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e68673ff0a29529606855b32bb45cd3c\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c31bb31d0c0a9ca9efeb309970d98416\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c31bb31d0c0a9ca9efeb309970d98416\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d33f8fcb8e6ec6de69937c85231a8678\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d33f8fcb8e6ec6de69937c85231a8678\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4edb4fa237ded69772f2a19f3222541d\transformed\library-1.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.f0ris.sweetalert:library:1.6.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\4edb4fa237ded69772f2a19f3222541d\transformed\library-1.6.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba59ae180d1651210b3c30b2387827ce\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ba59ae180d1651210b3c30b2387827ce\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [org.osmdroid:osmdroid-mapsforge:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\11993136c7a0cccc2ba2279295fd7c65\transformed\osmdroid-mapsforge-6.1.17\AndroidManifest.xml:5:5-7:41
MERGED from [org.osmdroid:osmdroid-mapsforge:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\11993136c7a0cccc2ba2279295fd7c65\transformed\osmdroid-mapsforge-6.1.17\AndroidManifest.xml:5:5-7:41
MERGED from [org.osmdroid:osmdroid-wms:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f6bb62380b6add8ae46e6e0b8a0f5737\transformed\osmdroid-wms-6.1.17\AndroidManifest.xml:5:5-7:40
MERGED from [org.osmdroid:osmdroid-wms:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f6bb62380b6add8ae46e6e0b8a0f5737\transformed\osmdroid-wms-6.1.17\AndroidManifest.xml:5:5-7:40
MERGED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:5:5-7:41
MERGED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efc2418746f9fe3f39d87378b1267f70\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\efc2418746f9fe3f39d87378b1267f70\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdba10c34d5ade66353f7678cb2763fc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cdba10c34d5ade66353f7678cb2763fc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8d2920a6edc1b5810fd0bd7b3f1c35f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f8d2920a6edc1b5810fd0bd7b3f1c35f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\004bc35284e5763afe11914b858aa4be\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\004bc35284e5763afe11914b858aa4be\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2fba1b995cf1d08f2e1291ba5fbeeef\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a2fba1b995cf1d08f2e1291ba5fbeeef\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1edd40a93e9f035a927ca216bcae34b0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\1edd40a93e9f035a927ca216bcae34b0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\113ae1c341d60d0ff34f97098af5c016\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\113ae1c341d60d0ff34f97098af5c016\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90ba95ff2dc6a4c83c2f4b7f3b902254\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\90ba95ff2dc6a4c83c2f4b7f3b902254\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\519ba7962a5760299625d441daaaeea3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\519ba7962a5760299625d441daaaeea3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\253687ec00bfffdc57e79b7379677ac6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\253687ec00bfffdc57e79b7379677ac6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e4acdb412f744a2dc37ff0dd9477f43e\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e4acdb412f744a2dc37ff0dd9477f43e\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b3a2f8e3db158eb1ad0e1657b32b52f\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5b3a2f8e3db158eb1ad0e1657b32b52f\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d7d83b64d29677e20bc3a10f58e24a88\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\d7d83b64d29677e20bc3a10f58e24a88\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e59e46cd690368fa6edf4bbaba71f687\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e59e46cd690368fa6edf4bbaba71f687\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c4c1618eb7fc9368656b131cf88c490\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2c4c1618eb7fc9368656b131cf88c490\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\60861d183da333a5e3e50b62b1bd06fe\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\60861d183da333a5e3e50b62b1bd06fe\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e8a622068c0edc7ae7492e5f95b8d5df\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e8a622068c0edc7ae7492e5f95b8d5df\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e9bc98679171a20e588e1fb7c8cf1c2\transformed\materialish-progress-1.7\AndroidManifest.xml:7:5-9:41
MERGED from [com.pnikosis:materialish-progress:1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6e9bc98679171a20e588e1fb7c8cf1c2\transformed\materialish-progress-1.7\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\app\src\main\AndroidManifest.xml
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
queries
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\a78adcd002a5a259abc5ce7e1ed6dd49\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9bfee985efd036317dc0500e2794d328\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\f136748a3cc2b2230abdfc86c83d7814\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\20446b1898b661f2716cf92ee44e5419\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c23986ee18d4b2088d094eeef538fa5d\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0ace8284c2b9780c83cb703116d87d75\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b90d1bc92e6b1c0595cb298b9b204732\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\21a7dfee5fba204655e8f763b02240a4\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c6351f6dce353b81e12ec7f17946396e\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\8da53752286d479d1379b3c11d542a16\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\df9d036fdf93feee8fcc3cbb70b204ce\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc844aef013f06e8390313dcbc2b798d\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\312b4823ba8aacd1db1ea586111deafc\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\631e8b8af57632c25e8bbc12d6e9c8f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\defe609f77158fc937c18027c8660b2d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\e94d5214879f1a6a7036926f638c5f17\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.mdsadrulhasan.gogolaundry.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.mdsadrulhasan.gogolaundry.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\98b62f2f5bc1112d0086d39f0eb418b1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5756b6d89d65a00bb787d09a7baccf0e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5a47ec4f36dba6d2801b3015f7956237\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
supports-screens
ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:9:5-12:40
	android:largeScreens
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:11:9-36
	android:normalScreens
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:12:9-37
	android:anyDensity
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:10:9-34
uses-feature#android.hardware.location.network
ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:14:5-16:36
	android:required
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:16:9-33
	android:name
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:15:9-57
uses-feature#android.hardware.location.gps
ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:17:5-19:36
	android:required
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:19:9-33
	android:name
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:18:9-53
uses-feature#android.hardware.telephony
ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:22:9-33
	android:name
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:21:9-50
uses-feature#android.hardware.wifi
ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:25:9-33
	android:name
		ADDED from [org.osmdroid:osmdroid-android:6.1.17] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\04c78b97304896bcd5afa736fff46d1e\transformed\osmdroid-android-6.1.17\AndroidManifest.xml:24:9-45
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\7571a22fb865177e2436ea52bd43b674\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\bb2e88dc948413094e8c597cf5263637\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\3645f15b7c9f91b8a8b342cf24f6f30f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\102269bae964aa109596160c2fca7623\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
