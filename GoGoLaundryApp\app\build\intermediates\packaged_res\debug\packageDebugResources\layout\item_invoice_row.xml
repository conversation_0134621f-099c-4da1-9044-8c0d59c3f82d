<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <TextView
        android:id="@+id/item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        tools:text="Shirt (Wash &amp; Iron)" />

    <TextView
        android:id="@+id/item_quantity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        tools:text="2" />

    <TextView
        android:id="@+id/item_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        tools:text="৳ 50.00" />

    <TextView
        android:id="@+id/item_total"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="end"
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        tools:text="৳ 100.00" />
</LinearLayout>
