<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="24dp"
    android:background="@drawable/glass_section_background"
    android:elevation="8dp"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header with Title and Close Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Filter Shops"
            android:textColor="@color/home_text_on_gradient"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Close Button -->
        <ImageButton
            android:id="@+id/closeButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/glass_button_background"
            android:src="@drawable/ic_close"
            android:contentDescription="@string/close"
            app:tint="@color/home_text_on_gradient" />

    </LinearLayout>

    <!-- Scrollable Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="500dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Distance Filter -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Distance Range"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/distanceSlider"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="1.0"
                        android:valueTo="50.0"
                        android:value="10.0"
                        android:stepSize="1.0"
                        app:thumbColor="@color/home_accent_blue"
                        app:trackColorActive="@color/home_accent_blue"
                        app:trackColorInactive="@color/glass_border" />

                    <TextView
                        android:id="@+id/distanceText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Within 10 km"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Rating Filter -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Minimum Rating"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/ratingSlider"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="0.0"
                        android:valueTo="5.0"
                        android:value="0.0"
                        android:stepSize="0.5"
                        app:thumbColor="@color/home_accent_blue"
                        app:trackColorActive="@color/home_accent_blue"
                        app:trackColorInactive="@color/glass_border" />

                    <TextView
                        android:id="@+id/ratingText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Any rating"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Services Filter -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Services Offered"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/serviceSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/glass_button_background"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

                <!-- Location Filter -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Location"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <!-- Division Spinner -->
                    <Spinner
                        android:id="@+id/divisionSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/glass_button_background"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp" />

                    <!-- District Spinner -->
                    <Spinner
                        android:id="@+id/districtSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/glass_button_background"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp" />

                    <!-- Upazilla Spinner -->
                    <Spinner
                        android:id="@+id/upazillaSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/glass_button_background"
                        android:padding="12dp"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

                <!-- Shop Status Filter -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Shop Status"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/openNowSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Open Now Only"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="14sp"
                        app:thumbTint="@color/home_accent_blue"
                        app:trackTint="@color/glass_border"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/verifiedOnlySwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Verified Shops Only"
                        android:textColor="@color/home_text_on_gradient"
                        android:textSize="14sp"
                        android:checked="true"
                        app:thumbTint="@color/home_accent_blue"
                        app:trackTint="@color/glass_border" />

                </LinearLayout>

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/clearFiltersButton"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="Clear All"
                        android:textColor="@color/home_text_on_gradient"
                        app:strokeColor="@color/glass_border" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/applyFiltersButton"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="Apply Filters"
                        android:textColor="@color/white"
                        android:backgroundTint="@color/home_accent_blue" />

                </LinearLayout>

            </LinearLayout>

    </ScrollView>

</LinearLayout>
