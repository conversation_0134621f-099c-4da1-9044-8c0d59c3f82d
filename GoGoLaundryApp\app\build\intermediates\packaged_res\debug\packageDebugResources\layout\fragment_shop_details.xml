<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark_new">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="280dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="@color/gradient_blue_start"
            app:expandedTitleTextAppearance="@style/TextAppearance.App.CollapsingToolbar.Expanded"
            app:collapsedTitleTextAppearance="@style/TextAppearance.App.CollapsingToolbar.Collapsed"
            app:expandedTitleMarginStart="24dp"
            app:expandedTitleMarginBottom="32dp">

            <!-- Shop Cover Image -->
            <ImageView
                android:id="@+id/shopCoverImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@drawable/gradient_blue_purple"
                app:layout_collapseMode="parallax"
                tools:src="@drawable/shop_cover_placeholder" />

            <!-- Enhanced Gradient Overlay -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_dark_overlay" />

            <!-- Toolbar -->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:navigationIcon="@drawable/ic_arrow_back"
                app:navigationIconTint="@color/white" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:background="@color/background_dark_new">

            <!-- Shop Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/card_background_dark"
                app:strokeColor="@color/colorPrimary"
                app:strokeWidth="2dp"
                app:cardElevation="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- Shop Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <!-- Shop Profile Image -->
                        <com.google.android.material.imageview.ShapeableImageView
                            android:id="@+id/shopProfileImageView"
                            android:layout_width="90dp"
                            android:layout_height="90dp"
                            android:layout_marginEnd="20dp"
                            android:scaleType="centerCrop"
                            android:background="@drawable/gradient_blue_purple"
                            app:shapeAppearanceOverlay="@style/CircularImageView"
                            app:strokeColor="@color/colorPrimary"
                            app:strokeWidth="3dp"
                            tools:src="@drawable/ic_shop_placeholder" />

                        <!-- Shop Details -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- Shop Name -->
                            <TextView
                                android:id="@+id/shopNameTextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/text_primary_dark"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:fontFamily="@font/kalpurush"
                                android:layout_marginBottom="8dp"
                                tools:text="Clean &amp; Fresh Laundry" />

                            <!-- Shop Address -->
                            <TextView
                                android:id="@+id/shopAddressTextView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:textColor="@color/text_secondary_dark"
                                android:textSize="16sp"
                                android:drawableStart="@drawable/ic_location"
                                android:drawablePadding="12dp"
                                android:lineSpacingExtra="4dp"
                                app:drawableTint="@color/colorPrimary"
                                tools:text="House 123, Road 15, Dhanmondi, Dhaka" />

                            <!-- Rating and Reviews -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:background="@drawable/rating_background"
                                android:padding="12dp">

                                <!-- Rating Stars -->
                                <LinearLayout
                                    android:id="@+id/ratingStarsLayout"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">
                                    <!-- Stars will be added programmatically -->
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/ratingTextView"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="12dp"
                                    android:textColor="@color/text_primary_dark"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="4.5" />

                                <TextView
                                    android:id="@+id/reviewCountTextView"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:textColor="@color/text_secondary_dark"
                                    android:textSize="14sp"
                                    tools:text="(123 reviews)" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Status Badge -->
                        <TextView
                            android:id="@+id/statusBadge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/badge_open"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="8dp"
                            android:text="@string/open"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:elevation="4dp" />

                    </LinearLayout>

                    <!-- Contact Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- Call Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/callButton"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="56dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="12dp"
                            android:text="@string/call"
                            android:textColor="@color/colorPrimary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:icon="@drawable/ic_phone"
                            app:iconTint="@color/colorPrimary"
                            app:iconSize="20dp"
                            app:strokeColor="@color/colorPrimary"
                            app:strokeWidth="2dp"
                            app:cornerRadius="16dp" />

                        <!-- Directions Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/directionsButton"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="56dp"
                            android:layout_weight="1"
                            android:layout_marginStart="12dp"
                            android:text="@string/directions"
                            android:textColor="@color/colorPrimary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:icon="@drawable/ic_directions"
                            app:iconTint="@color/colorPrimary"
                            app:iconSize="20dp"
                            app:strokeColor="@color/colorPrimary"
                            app:strokeWidth="2dp"
                            app:cornerRadius="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Operating Hours Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/card_background_dark"
                app:strokeColor="@color/colorPrimary"
                app:strokeWidth="1dp"
                app:cardElevation="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/operating_hours"
                        android:textColor="@color/text_primary_dark"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/operatingHoursRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_operating_hours" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Services and Items Tabs -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="20dp"
                app:cardBackgroundColor="@color/card_background_dark"
                app:strokeColor="@color/colorPrimary"
                app:strokeWidth="1dp"
                app:cardElevation="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Tab Layout -->
                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        app:tabTextColor="@color/text_secondary_dark"
                        app:tabSelectedTextColor="@color/text_primary_dark"
                        app:tabIndicatorColor="@color/colorPrimary"
                        app:tabIndicatorHeight="4dp"
                        app:tabTextAppearance="@style/TabTextAppearance" />

                    <!-- ViewPager2 -->
                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/viewPager"
                        android:layout_width="match_parent"
                        android:layout_height="400dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Modern Place Order Button -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/orderFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_margin="24dp"
        android:text="@string/place_order"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        android:elevation="12dp"
        app:icon="@drawable/ic_shopping_cart"
        app:iconTint="@color/white"
        app:iconSize="24dp"
        app:backgroundTint="@color/colorPrimary"
        app:cornerRadius="28dp"
        app:rippleColor="@color/white_30" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
