<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:id="@+id/invoice_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/company_logo"
                android:layout_width="120dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/logo" />

            <TextView
                android:id="@+id/invoice_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="INVOICE"
                android:textColor="@color/primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="@color/divider" />
        </LinearLayout>

        <!-- Invoice Info -->
        <LinearLayout
            android:id="@+id/invoice_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@id/invoice_header">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Invoice To:"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/customer_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="John Doe" />

                <TextView
                    android:id="@+id/customer_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="+880 1234567890" />

                <TextView
                    android:id="@+id/customer_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="123 Main St, Dhaka" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Invoice Details:"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/invoice_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="Invoice #: INV-12345" />

                <TextView
                    android:id="@+id/order_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="Order #: ORD-12345" />

                <TextView
                    android:id="@+id/invoice_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="Date: Jan 01, 2023" />
            </LinearLayout>
        </LinearLayout>

        <!-- Items Header -->
        <LinearLayout
            android:id="@+id/items_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@color/primary"
            android:orientation="horizontal"
            android:padding="8dp"
            app:layout_constraintTop_toBottomOf="@id/invoice_info">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="Item"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="Qty"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="Price"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:text="Total"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Items List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/items_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/items_header"
            tools:itemCount="3"
            tools:listitem="@layout/item_invoice_row" />

        <!-- Summary -->
        <LinearLayout
            android:id="@+id/invoice_summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/items_recycler_view">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:padding="4dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="end"
                    android:text="Subtotal:"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/subtotal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="end"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="৳ 500.00" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="4dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="end"
                    android:text="Discount:"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/discount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="end"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="৳ 50.00" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="4dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="end"
                    android:text="Delivery Fee:"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/delivery_fee"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="end"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    tools:text="৳ 60.00" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:background="@color/divider" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:padding="4dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="end"
                    android:text="Total:"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/total"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="end"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="৳ 510.00" />
            </LinearLayout>
        </LinearLayout>

        <!-- Footer -->
        <LinearLayout
            android:id="@+id/invoice_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/invoice_summary">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="Thank you for your business!"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="For any questions, please contact us at:"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="+880 1234567890 | <EMAIL>"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@id/invoice_footer">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/share_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="Share"
                app:icon="@drawable/ic_share" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/print_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="Print"
                app:icon="@drawable/ic_print" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
