<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/search_ripple_effect">
    
    <item>
        <layer-list>
            
            <!-- Background circle with gradient -->
            <item>
                <shape android:shape="oval">
                    <gradient
                        android:startColor="@color/search_background_primary"
                        android:endColor="@color/search_background_secondary"
                        android:angle="135" />
                </shape>
            </item>
            
            <!-- Border for glass effect -->
            <item>
                <shape android:shape="oval">
                    <stroke
                        android:width="1.5dp"
                        android:color="@color/search_border_primary" />
                </shape>
            </item>
            
            <!-- Inner highlight -->
            <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
                <shape android:shape="oval">
                    <stroke
                        android:width="0.5dp"
                        android:color="@color/search_highlight" />
                </shape>
            </item>
            
        </layer-list>
    </item>
    
</ripple>
