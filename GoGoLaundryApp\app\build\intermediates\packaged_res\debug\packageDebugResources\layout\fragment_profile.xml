<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="6dp"
        android:paddingTop="6dp"
        android:paddingEnd="6dp"
        android:paddingBottom="16dp">

        <!-- Profile Header Section with Glass Effect -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/profile_header_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_hero_background"
            android:elevation="8dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Profile Title with Icon -->
            <LinearLayout
                android:id="@+id/profile_title_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_person"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:id="@+id/profile_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="My Profile"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/home_text_on_gradient"
                    android:textStyle="bold"
                    android:letterSpacing="0.02" />

            </LinearLayout>

            <!-- Profile Image Container with Glass Effect -->
            <FrameLayout
                android:id="@+id/profile_image_outer_container"
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/glass_icon_background"
                android:elevation="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/profile_title_container">

                <FrameLayout
                    android:id="@+id/profile_image_container"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_gravity="center">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/profile_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="3dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_person"
                        app:shapeAppearanceOverlay="@style/CircleImageView"
                        app:strokeColor="@color/home_accent_blue"
                        app:strokeWidth="3dp" />

                    <!-- Edit Button with Glass Effect -->
                    <FrameLayout
                        android:id="@+id/edit_profile_image"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="bottom|end"
                        android:layout_marginEnd="4dp"
                        android:layout_marginBottom="4dp"
                        android:background="@drawable/glass_button_background"
                        android:elevation="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackgroundBorderless">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_edit"
                            app:tint="@color/home_accent_blue" />

                    </FrameLayout>

                </FrameLayout>

            </FrameLayout>

            <!-- User Name Display -->
            <TextView
                android:id="@+id/user_name_display"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="John Doe"
                android:textAlignment="center"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                android:textColor="@color/home_text_on_gradient"
                android:textStyle="bold"
                android:alpha="0.95"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/profile_image_outer_container" />

            <!-- User Status -->
            <TextView
                android:id="@+id/user_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="Premium Member"
                android:textAlignment="center"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                android:textColor="@color/home_text_on_gradient"
                android:alpha="0.8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/user_name_display" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Personal Information Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/personal_info_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/profile_header_section">

            <!-- Section Title -->
            <LinearLayout
                android:id="@+id/personal_info_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_details"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Personal Information"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Form Fields Container with Glass Effect -->
            <FrameLayout
                android:id="@+id/form_fields_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="20dp"
                app:layout_constraintTop_toBottomOf="@id/personal_info_header">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- Full Name Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/full_name_layout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Full Name"
                        app:boxBackgroundColor="@color/glass_input_background"
                        app:boxStrokeColor="@color/home_accent_blue"
                        app:hintTextColor="@color/text_secondary"
                        app:layout_constraintTop_toTopOf="parent"
                        app:startIconDrawable="@drawable/ic_person"
                        app:startIconTint="@color/home_accent_blue">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/full_name_edit_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName"
                            android:textColor="@color/text_primary"
                            android:textColorHint="@color/text_secondary" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Phone Number Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/phone_layout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="Phone Number"
                        app:boxBackgroundColor="@color/glass_input_background"
                        app:boxStrokeColor="@color/home_accent_blue"
                        app:hintTextColor="@color/text_secondary"
                        app:layout_constraintTop_toBottomOf="@id/full_name_layout"
                        app:startIconDrawable="@drawable/ic_phone"
                        app:startIconTint="@color/home_accent_blue">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/phone_edit_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:enabled="false"
                            android:inputType="phone"
                            android:textColor="@color/text_primary"
                            android:textColorHint="@color/text_secondary" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Email Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/email_layout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="Email Address"
                        app:boxBackgroundColor="@color/glass_input_background"
                        app:boxStrokeColor="@color/home_accent_blue"
                        app:hintTextColor="@color/text_secondary"
                        app:layout_constraintTop_toBottomOf="@id/phone_layout"
                        app:startIconDrawable="@drawable/ic_email"
                        app:startIconTint="@color/home_accent_blue">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/email_edit_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress"
                            android:textColor="@color/text_primary"
                            android:textColorHint="@color/text_secondary" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Address Input -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/address_layout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="Delivery Address"
                        app:boxBackgroundColor="@color/glass_input_background"
                        app:boxStrokeColor="@color/home_accent_blue"
                        app:hintTextColor="@color/text_secondary"
                        app:layout_constraintTop_toBottomOf="@id/email_layout"
                        app:startIconDrawable="@drawable/ic_location"
                        app:startIconTint="@color/home_accent_blue">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/address_edit_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPostalAddress"
                            android:minLines="2"
                            android:textColor="@color/text_primary"
                            android:textColorHint="@color/text_secondary" />
                    </com.google.android.material.textfield.TextInputLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

            <!-- Save Button with Glass Effect -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/save_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="Save Changes"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:backgroundTint="@color/home_accent_blue"
                android:elevation="8dp"
                app:cornerRadius="25dp"
                app:icon="@drawable/ic_check"
                app:iconTint="@android:color/white"
                app:iconGravity="textStart"
                app:iconPadding="12dp"
                app:layout_constraintTop_toBottomOf="@id/form_fields_container"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Security Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/security_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/personal_info_section">

            <!-- Section Title -->
            <LinearLayout
                android:id="@+id/security_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_lock"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Security Settings"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Security Options Container -->
            <FrameLayout
                android:id="@+id/security_options_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="20dp"
                app:layout_constraintTop_toBottomOf="@id/security_header">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- Change Password Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/change_password_button"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Change Password"
                        android:textColor="@color/home_accent_blue"
                        android:textStyle="bold"
                        app:cornerRadius="20dp"
                        app:icon="@drawable/ic_lock"
                        app:iconTint="@color/home_accent_blue"
                        app:iconGravity="textStart"
                        app:iconPadding="12dp"
                        app:strokeColor="@color/home_accent_blue"
                        app:strokeWidth="2dp"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- Logout Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/logout_button"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="Logout"
                        android:textColor="@color/error_color"
                        android:textStyle="bold"
                        app:cornerRadius="20dp"
                        app:icon="@drawable/ic_logout"
                        app:iconTint="@color/error_color"
                        app:iconGravity="textStart"
                        app:iconPadding="12dp"
                        app:strokeColor="@color/error_color"
                        app:strokeWidth="2dp"
                        app:layout_constraintTop_toBottomOf="@id/change_password_button" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Account Statistics Section -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/stats_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="24dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/security_section"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- Section Title -->
            <LinearLayout
                android:id="@+id/stats_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_dashboard"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Account Statistics"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Statistics Grid -->
            <LinearLayout
                android:id="@+id/stats_grid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="horizontal"
                android:weightSum="3"
                app:layout_constraintTop_toBottomOf="@id/stats_header">

                <!-- Total Orders -->
                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/glass_section_background"
                    android:elevation="4dp"
                    android:padding="16dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/orders_icon"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_receipt"
                            app:tint="@color/home_accent_blue"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent" />

                        <TextView
                            android:id="@+id/total_orders_count"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="12"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold"
                            app:layout_constraintTop_toBottomOf="@id/orders_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="Orders"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            app:layout_constraintTop_toBottomOf="@id/total_orders_count" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </FrameLayout>

                <!-- Total Spent -->
                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:background="@drawable/glass_section_background"
                    android:elevation="4dp"
                    android:padding="16dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/spent_icon"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_cart"
                            app:tint="@color/home_accent_blue"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent" />

                        <TextView
                            android:id="@+id/total_spent_amount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="৳2,450"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold"
                            app:layout_constraintTop_toBottomOf="@id/spent_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="Spent"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            app:layout_constraintTop_toBottomOf="@id/total_spent_amount" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </FrameLayout>

                <!-- Member Since -->
                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/glass_section_background"
                    android:elevation="4dp"
                    android:padding="16dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/member_icon"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_calendar"
                            app:tint="@color/home_accent_blue"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent" />

                        <TextView
                            android:id="@+id/member_since_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="2024"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold"
                            app:layout_constraintTop_toBottomOf="@id/member_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="Member"
                            android:textAlignment="center"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                            android:textColor="@color/text_secondary"
                            app:layout_constraintTop_toBottomOf="@id/member_since_date" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </FrameLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
