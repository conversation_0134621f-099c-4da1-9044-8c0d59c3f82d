<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Orders Management';
$currentPage = 'orders';

// Get filter parameters
$status = isset($_GET['status']) ? sanitize($_GET['status']) : 'all';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Handle order status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_status':
            $orderId = intval($_POST['order_id']);
            $newStatus = sanitize($_POST['new_status']);
            
            try {
                $updateStmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ? AND shop_id = ?");
                if ($updateStmt->execute([$newStatus, $orderId, $shopOwnerData['shop_id']])) {
                    $_SESSION['shop_success_message'] = 'Order status updated successfully!';
                } else {
                    $_SESSION['shop_error_message'] = 'Failed to update order status.';
                }
            } catch (PDOException $e) {
                error_log('Order status update error: ' . $e->getMessage());
                $_SESSION['shop_error_message'] = 'Failed to update order status.';
            }
            break;
    }
}

// Build query conditions
$whereClause = "WHERE o.shop_id = ?";
$params = [$shopOwnerData['shop_id']];

if ($status !== 'all') {
    $whereClause .= " AND o.status = ?";
    $params[] = $status;
}

if (!empty($search)) {
    $whereClause .= " AND (u.full_name LIKE ? OR u.phone LIKE ? OR o.id LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

// Get total count
$countSql = "SELECT COUNT(*) FROM orders o LEFT JOIN users u ON o.user_id = u.id $whereClause";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalOrders = $countStmt->fetchColumn();
$totalPages = ceil($totalOrders / $limit);

// Get orders
$sql = "
    SELECT o.*, u.full_name as customer_name, u.phone as customer_phone, u.address as customer_address,
           COUNT(oi.id) as total_items
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    LEFT JOIN order_items oi ON o.id = oi.order_id
    $whereClause
    GROUP BY o.id
    ORDER BY o.created_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $limit;
$params[] = $offset;

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get order status counts
$statusCountsStmt = $pdo->prepare("
    SELECT status, COUNT(*) as count 
    FROM orders 
    WHERE shop_id = ? 
    GROUP BY status
");
$statusCountsStmt->execute([$shopOwnerData['shop_id']]);
$statusCounts = [];
while ($row = $statusCountsStmt->fetch(PDO::FETCH_ASSOC)) {
    $statusCounts[$row['status']] = $row['count'];
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Orders Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<!-- Order Status Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">New Orders</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo $statusCounts['placed'] ?? 0; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Processing</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo $statusCounts['processing'] ?? 0; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Out for Delivery</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo $statusCounts['out_for_delivery'] ?? 0; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo $statusCounts['delivered'] ?? 0; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="<?= htmlspecialchars($search) ?>" placeholder="Customer name, phone, order ID...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Orders</option>
                    <option value="placed" <?= $status === 'placed' ? 'selected' : '' ?>>New Orders</option>
                    <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>Processing</option>
                    <option value="out_for_delivery" <?= $status === 'out_for_delivery' ? 'selected' : '' ?>>Out for Delivery</option>
                    <option value="delivered" <?= $status === 'delivered' ? 'selected' : '' ?>>Completed</option>
                    <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="orders.php" class="btn btn-outline-secondary">Reset</a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Orders (<?= $totalOrders ?> total)</h5>
    </div>
    <div class="card-body">
        <?php if (empty($orders)): ?>
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <p class="text-muted">No orders found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Items</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><strong>#<?= $order['id'] ?></strong></td>
                                <td>
                                    <?= htmlspecialchars($order['customer_name'] ?? 'N/A') ?>
                                    <br><small class="text-muted"><?= htmlspecialchars($order['customer_phone'] ?? '') ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= $order['total_items'] ?> items</span>
                                </td>
                                <td>৳<?= number_format($order['total'], 2) ?></td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    $statusText = '';
                                    switch ($order['status']) {
                                        case 'placed': 
                                            $statusClass = 'bg-warning text-dark'; 
                                            $statusText = 'New Order';
                                            break;
                                        case 'processing': 
                                            $statusClass = 'bg-info'; 
                                            $statusText = 'Processing';
                                            break;
                                        case 'out_for_delivery': 
                                            $statusClass = 'bg-primary'; 
                                            $statusText = 'Out for Delivery';
                                            break;
                                        case 'delivered': 
                                            $statusClass = 'bg-success'; 
                                            $statusText = 'Completed';
                                            break;
                                        case 'cancelled': 
                                            $statusClass = 'bg-danger'; 
                                            $statusText = 'Cancelled';
                                            break;
                                        default: 
                                            $statusClass = 'bg-secondary';
                                            $statusText = ucfirst($order['status']);
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                                <td>
                                    <?= date('M j, Y', strtotime($order['created_at'])) ?>
                                    <br><small class="text-muted"><?= date('g:i A', strtotime($order['created_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                                data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="order_details.php?id=<?= $order['id'] ?>">
                                                <i class="fas fa-eye"></i> View Details
                                            </a></li>
                                            <?php if ($order['status'] !== 'delivered' && $order['status'] !== 'cancelled'): ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button type="button" class="dropdown-item" 
                                                            onclick="updateOrderStatus(<?= $order['id'] ?>, '<?= $order['status'] ?>')">
                                                        <i class="fas fa-edit"></i> Update Status
                                                    </button>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Orders pagination">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="order_id" id="update_order_id">
                    
                    <div class="alert alert-info">
                        <strong>Order #<span id="update_order_display_id"></span></strong>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status</label>
                        <select class="form-select" id="new_status" name="new_status" required>
                            <option value="placed">New Order</option>
                            <option value="processing">Processing</option>
                            <option value="out_for_delivery">Out for Delivery</option>
                            <option value="delivered">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }
</style>

<script>
function updateOrderStatus(orderId, currentStatus) {
    document.getElementById('update_order_id').value = orderId;
    document.getElementById('update_order_display_id').textContent = orderId;
    document.getElementById('new_status').value = currentStatus;
    
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
