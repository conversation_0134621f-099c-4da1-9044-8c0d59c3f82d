<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Order Analytics';
$currentPage = 'order_report';

// Get analytics data
try {
    // Order status distribution
    $statusStmt = $pdo->prepare("
        SELECT status, COUNT(*) as count 
        FROM orders 
        WHERE shop_id = ? 
        GROUP BY status
        ORDER BY count DESC
    ");
    $statusStmt->execute([$shopOwnerData['shop_id']]);
    $statusData = $statusStmt->fetchAll(PDO::FETCH_ASSOC);

    // Monthly order trends (last 12 months)
    $monthlyStmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
            COALESCE(SUM(CASE WHEN status = 'delivered' THEN total ELSE 0 END), 0) as revenue
        FROM orders 
        WHERE shop_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $monthlyStmt->execute([$shopOwnerData['shop_id']]);
    $monthlyData = $monthlyStmt->fetchAll(PDO::FETCH_ASSOC);

    // Service performance
    $serviceStmt = $pdo->prepare("
        SELECT 
            s.name as service_name,
            COUNT(DISTINCT o.id) as order_count,
            SUM(oi.quantity) as total_items,
            COALESCE(SUM(oi.price * oi.quantity), 0) as revenue
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN items i ON oi.item_id = i.id
        JOIN services s ON i.service_id = s.id
        WHERE o.shop_id = ? AND o.status = 'delivered'
        GROUP BY s.id
        ORDER BY revenue DESC
    ");
    $serviceStmt->execute([$shopOwnerData['shop_id']]);
    $serviceData = $serviceStmt->fetchAll(PDO::FETCH_ASSOC);

    // Peak hours analysis
    $hourlyStmt = $pdo->prepare("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as order_count
        FROM orders 
        WHERE shop_id = ? 
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    $hourlyStmt->execute([$shopOwnerData['shop_id']]);
    $hourlyData = $hourlyStmt->fetchAll(PDO::FETCH_ASSOC);

    // Customer analytics
    $customerStmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT user_id) as total_customers,
            COUNT(*) / COUNT(DISTINCT user_id) as avg_orders_per_customer,
            COUNT(CASE WHEN order_count > 1 THEN 1 END) as repeat_customers
        FROM (
            SELECT user_id, COUNT(*) as order_count
            FROM orders 
            WHERE shop_id = ?
            GROUP BY user_id
        ) customer_orders
    ");
    $customerStmt->execute([$shopOwnerData['shop_id']]);
    $customerAnalytics = $customerStmt->fetch(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log('Order analytics error: ' . $e->getMessage());
    $statusData = [];
    $monthlyData = [];
    $serviceData = [];
    $hourlyData = [];
    $customerAnalytics = ['total_customers' => 0, 'avg_orders_per_customer' => 0, 'repeat_customers' => 0];
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Order Analytics</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.print()">
            <i class="fas fa-print me-1"></i>Print Report
        </button>
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<!-- Customer Analytics Cards -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Customers</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($customerAnalytics['total_customers']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Repeat Customers</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($customerAnalytics['repeat_customers']) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Orders/Customer</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($customerAnalytics['avg_orders_per_customer'], 1) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Order Status Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Order Status Distribution
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>

    <!-- Service Performance -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-concierge-bell me-2"></i>Service Performance
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($serviceData)): ?>
                    <p class="text-muted text-center">No service data available.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Orders</th>
                                    <th>Items</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($serviceData as $service): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($service['service_name']) ?></td>
                                        <td><?= number_format($service['order_count']) ?></td>
                                        <td><?= number_format($service['total_items']) ?></td>
                                        <td>৳<?= number_format($service['revenue'], 2) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chart-line me-2"></i>Monthly Order Trends (Last 12 Months)
        </h5>
    </div>
    <div class="card-body">
        <canvas id="monthlyChart" width="400" height="200"></canvas>
    </div>
</div>

<!-- Peak Hours Analysis -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-clock me-2"></i>Peak Hours Analysis
        </h5>
    </div>
    <div class="card-body">
        <canvas id="hourlyChart" width="400" height="200"></canvas>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }

@media print {
    .btn-toolbar, .card-header .btn, .sidebar, .navbar { display: none !important; }
    .content { margin-left: 0 !important; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Order Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusData = <?= json_encode($statusData) ?>;
    
    const statusLabels = statusData.map(item => {
        switch(item.status) {
            case 'placed': return 'New Orders';
            case 'processing': return 'Processing';
            case 'out_for_delivery': return 'Out for Delivery';
            case 'delivered': return 'Completed';
            case 'cancelled': return 'Cancelled';
            default: return item.status;
        }
    });
    const statusCounts = statusData.map(item => parseInt(item.count));
    
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels,
            datasets: [{
                data: statusCounts,
                backgroundColor: [
                    '#ffc107',
                    '#17a2b8',
                    '#007bff',
                    '#28a745',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Trends Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    const monthlyData = <?= json_encode($monthlyData) ?>;
    
    const monthLabels = monthlyData.map(item => {
        const date = new Date(item.month + '-01');
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    const totalOrders = monthlyData.map(item => parseInt(item.total_orders));
    const completedOrders = monthlyData.map(item => parseInt(item.completed_orders));
    
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: monthLabels,
            datasets: [{
                label: 'Total Orders',
                data: totalOrders,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: 'Completed Orders',
                data: completedOrders,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Hourly Chart
    const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
    const hourlyData = <?= json_encode($hourlyData) ?>;
    
    // Create 24-hour array with data
    const hourlyArray = new Array(24).fill(0);
    hourlyData.forEach(item => {
        hourlyArray[parseInt(item.hour)] = parseInt(item.order_count);
    });
    
    const hourLabels = Array.from({length: 24}, (_, i) => {
        const hour = i === 0 ? 12 : i > 12 ? i - 12 : i;
        const ampm = i < 12 ? 'AM' : 'PM';
        return `${hour}:00 ${ampm}`;
    });
    
    new Chart(hourlyCtx, {
        type: 'bar',
        data: {
            labels: hourLabels,
            datasets: [{
                label: 'Orders',
                data: hourlyArray,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
