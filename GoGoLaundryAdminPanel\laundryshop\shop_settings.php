<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Shop Settings';
$currentPage = 'shop_settings';

// Get shop details
try {
    $shopStmt = $pdo->prepare("SELECT * FROM laundry_shops WHERE id = ?");
    $shopStmt->execute([$shopOwnerData['shop_id']]);
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Shop settings fetch error: ' . $e->getMessage());
    $shop = null;
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_operating_hours':
            $operatingHours = sanitize($_POST['operating_hours']);
            
            try {
                $stmt = $pdo->prepare("UPDATE laundry_shops SET operating_hours = ?, updated_at = NOW() WHERE id = ?");
                if ($stmt->execute([$operatingHours, $shopOwnerData['shop_id']])) {
                    $_SESSION['shop_success_message'] = 'Operating hours updated successfully!';
                    // Refresh shop data
                    $shopStmt->execute([$shopOwnerData['shop_id']]);
                    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
                } else {
                    $_SESSION['shop_error_message'] = 'Failed to update operating hours.';
                }
            } catch (PDOException $e) {
                error_log('Operating hours update error: ' . $e->getMessage());
                $_SESSION['shop_error_message'] = 'Failed to update operating hours.';
            }
            break;
            
        case 'update_location':
            $latitude = floatval($_POST['latitude']);
            $longitude = floatval($_POST['longitude']);
            $address = sanitize($_POST['address']);
            
            if ($latitude != 0 && $longitude != 0) {
                try {
                    $stmt = $pdo->prepare("UPDATE laundry_shops SET latitude = ?, longitude = ?, address = ?, updated_at = NOW() WHERE id = ?");
                    if ($stmt->execute([$latitude, $longitude, $address, $shopOwnerData['shop_id']])) {
                        $_SESSION['shop_success_message'] = 'Location updated successfully!';
                        // Refresh shop data
                        $shopStmt->execute([$shopOwnerData['shop_id']]);
                        $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $_SESSION['shop_error_message'] = 'Failed to update location.';
                    }
                } catch (PDOException $e) {
                    error_log('Location update error: ' . $e->getMessage());
                    $_SESSION['shop_error_message'] = 'Failed to update location.';
                }
            } else {
                $_SESSION['shop_error_message'] = 'Please select a valid location on the map.';
            }
            break;
    }
}

if (!$shop) {
    $_SESSION['shop_error_message'] = 'Shop not found.';
    header('Location: index.php');
    exit;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Shop Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shop['name']); ?>
        </span>
    </div>
</div>

<div class="row">
    <!-- Operating Hours -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Operating Hours
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_operating_hours">
                    
                    <div class="mb-3">
                        <label for="operating_hours" class="form-label">Operating Hours</label>
                        <textarea class="form-control" id="operating_hours" name="operating_hours" rows="4" 
                                  placeholder="e.g., Monday - Friday: 8:00 AM - 8:00 PM&#10;Saturday: 9:00 AM - 6:00 PM&#10;Sunday: Closed"><?php echo htmlspecialchars($shop['operating_hours'] ?? ''); ?></textarea>
                        <div class="form-text">Enter your shop's operating hours. This will be displayed to customers.</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Operating Hours
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Location Settings -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>Location Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_location">
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="2" required><?php echo htmlspecialchars($shop['address']); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Shop Location</label>
                        <div id="map" style="height: 300px; border-radius: 8px;"></div>
                        <small class="text-muted">Click on the map to update your shop location</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" class="form-control" id="latitude" name="latitude" 
                                       step="any" value="<?php echo $shop['latitude']; ?>" required readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" class="form-control" id="longitude" name="longitude" 
                                       step="any" value="<?php echo $shop['longitude']; ?>" required readonly>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Location
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Shop Information Display -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Shop Information
                </h5>
                <small class="text-muted">Read-only information managed by admin</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Shop ID</label>
                            <div class="form-control-plaintext">#<?php echo $shop['id']; ?></div>
                        </div>
                        
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Commission Rate</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary"><?php echo number_format($shop['commission_percentage'], 1); ?>%</span>
                            </div>
                        </div>
                        
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Verification Status</label>
                            <div class="form-control-plaintext">
                                <?php if ($shop['is_verified']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Verified
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Pending Verification
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Shop Status</label>
                            <div class="form-control-plaintext">
                                <?php if ($shop['is_active']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>Inactive
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Rating</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i><?php echo number_format($shop['rating'], 1); ?>
                                </span>
                                <small class="text-muted ms-2">(<?php echo number_format($shop['total_reviews']); ?> reviews)</small>
                            </div>
                        </div>
                        
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Member Since</label>
                            <div class="form-control-plaintext">
                                <?php echo date('M j, Y', strtotime($shop['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> To update shop name, description, contact details, or other basic information, 
                    please visit the <a href="profile.php" class="alert-link">Shop Profile</a> page.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

<style>
.info-group {
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 10px;
}

.info-group:last-child {
    border-bottom: none;
}

.form-control-plaintext {
    font-weight: 500;
}
</style>

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let marker;

// Initialize map
document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
});

function initializeMap() {
    const currentLat = <?php echo $shop['latitude']; ?>;
    const currentLng = <?php echo $shop['longitude']; ?>;
    
    // Initialize map with current shop location
    map = L.map('map').setView([currentLat, currentLng], 15);
    
    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Add current location marker
    marker = L.marker([currentLat, currentLng]).addTo(map);
    
    // Add click listener to map
    map.on('click', function(e) {
        setMapLocation(e.latlng.lat, e.latlng.lng);
    });
}

function setMapLocation(lat, lng) {
    // Remove existing marker
    if (marker) {
        map.removeLayer(marker);
    }
    
    // Add new marker
    marker = L.marker([lat, lng]).addTo(map);
    
    // Update coordinate inputs
    document.getElementById('latitude').value = lat.toFixed(8);
    document.getElementById('longitude').value = lng.toFixed(8);
    
    // Center map on location
    map.setView([lat, lng], map.getZoom());
}
</script>

<?php include 'includes/footer.php'; ?>
