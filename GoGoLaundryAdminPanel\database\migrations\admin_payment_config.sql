-- Admin Payment Configuration Migration
-- This migration adds admin payment settings to ensure users pay to admin accounts

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS `admin_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL UNIQUE,
    `setting_value` text NOT NULL,
    `description` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert admin payment configuration settings
INSERT INTO `admin_settings` (`setting_key`, `setting_value`, `description`) VALUES
('admin_bkash_number', '***********', 'Admin bKash account number for receiving payments'),
('admin_nagad_number', '***********', 'Admin Nagad account number for receiving payments'),
('admin_rocket_number', '***********', 'Admin Rocket account number for receiving payments'),
('admin_payment_instructions', 'Please send payment to the admin account numbers provided. Include your order number in the transaction reference for faster processing.', 'Instructions shown to users during payment'),
('payment_verification_required', '1', 'Whether payment verification is required (1 = yes, 0 = no)'),
('admin_whatsapp', '+*************', 'Admin WhatsApp number for customer support'),
('delivery_fee', '50.00', 'Default delivery fee amount'),
('commission_rate', '15.00', 'Default commission rate percentage for shops'),
('app_maintenance_mode', '0', 'App maintenance mode (1 = enabled, 0 = disabled)'),
('app_version', '1.0.0', 'Current app version')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `description` = VALUES(`description`),
    `updated_at` = CURRENT_TIMESTAMP;

-- Update existing settings table if it exists (for backward compatibility)
UPDATE `settings` SET 
    `value` = '***********' 
WHERE `key` = 'admin_bkash_number' AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'settings');

UPDATE `settings` SET 
    `value` = '***********' 
WHERE `key` = 'admin_nagad_number' AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'settings');

UPDATE `settings` SET 
    `value` = '***********' 
WHERE `key` = 'admin_rocket_number' AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'settings');

-- Create index for better performance
CREATE INDEX IF NOT EXISTS `idx_admin_settings_key` ON `admin_settings` (`setting_key`);

-- Insert sample admin payment transaction log table for tracking
CREATE TABLE IF NOT EXISTS `admin_payment_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `payment_method` enum('bkash', 'nagad', 'rocket', 'card', 'cash') NOT NULL,
    `admin_account_number` varchar(20) DEFAULT NULL,
    `transaction_id` varchar(100) DEFAULT NULL,
    `amount` decimal(10,2) NOT NULL,
    `status` enum('pending', 'verified', 'rejected') NOT NULL DEFAULT 'pending',
    `verification_notes` text DEFAULT NULL,
    `verified_by` int(11) DEFAULT NULL,
    `verified_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_payment_method` (`payment_method`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraints if tables exist
-- ALTER TABLE `admin_payment_logs` 
-- ADD CONSTRAINT `fk_admin_payment_logs_order` 
-- FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- ALTER TABLE `admin_payment_logs` 
-- ADD CONSTRAINT `fk_admin_payment_logs_user` 
-- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Create view for easy payment tracking
CREATE OR REPLACE VIEW `admin_payment_summary` AS
SELECT 
    apl.payment_method,
    apl.admin_account_number,
    COUNT(*) as total_transactions,
    SUM(apl.amount) as total_amount,
    SUM(CASE WHEN apl.status = 'verified' THEN apl.amount ELSE 0 END) as verified_amount,
    SUM(CASE WHEN apl.status = 'pending' THEN apl.amount ELSE 0 END) as pending_amount,
    SUM(CASE WHEN apl.status = 'rejected' THEN apl.amount ELSE 0 END) as rejected_amount
FROM admin_payment_logs apl
GROUP BY apl.payment_method, apl.admin_account_number;

-- Insert initial admin user if not exists (for testing)
INSERT IGNORE INTO `users` (`name`, `email`, `phone`, `password`, `role`, `is_active`, `created_at`) 
VALUES ('Admin User', '<EMAIL>', '***********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, NOW());

-- Success message
SELECT 'Admin payment configuration migration completed successfully!' as message;
