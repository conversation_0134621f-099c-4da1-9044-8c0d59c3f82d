package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.imageview.ShapeableImageView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;
import com.mdsadrulhasan.gogolaundry.ui.adapter.ShopDetailsAdapter;
import com.mdsadrulhasan.gogolaundry.ui.fragment.CheckoutFragment;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.ShopDetailsViewModel;

/**
 * Fragment for displaying detailed shop information with enhanced RecyclerView implementation
 */
public class ShopDetailsFragment extends Fragment implements ShopDetailsAdapter.OnItemClickListener {

    private static final String TAG = "ShopDetailsFragment";
    private static final String ARG_SHOP_ID = "shop_id";

    // Views
    private CollapsingToolbarLayout collapsingToolbar;
    private Toolbar toolbar;
    private ImageView shopCoverImageView;
    private ShapeableImageView shopProfileImageView;
    private TextView shopNameTextView;
    private TextView shopAddressTextView;
    private LinearLayout ratingStarsLayout;
    private TextView ratingTextView;
    private TextView reviewCountTextView;
    private TextView statusBadge;
    private MaterialButton callButton;
    private MaterialButton directionsButton;
    private RecyclerView detailsRecyclerView;
    private ExtendedFloatingActionButton orderFab;

    // ViewModel
    private ShopDetailsViewModel viewModel;

    // Enhanced Adapter (replaces ViewPager2 as requested)
    private ShopDetailsAdapter detailsAdapter;

    // Shop data
    private int shopId;

    public static ShopDetailsFragment newInstance(int shopId) {
        ShopDetailsFragment fragment = new ShopDetailsFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_SHOP_ID, shopId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            shopId = getArguments().getInt(ARG_SHOP_ID);
        }

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(ShopDetailsViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_shop_details, container, false);

        initializeViews(view);
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        observeViewModel();

        // Load shop data
        Log.d(TAG, "Loading shop details for shopId: " + shopId);
        Log.d(TAG, "ViewModel instance: " + (viewModel != null ? "initialized" : "null"));
        Log.d(TAG, "About to call viewModel.setShopId(" + shopId + ")");
        viewModel.setShopId(shopId);
        Log.d(TAG, "Called viewModel.setShopId(" + shopId + ") successfully");

        return view;
    }

    private void initializeViews(View view) {
        collapsingToolbar = view.findViewById(R.id.collapsingToolbar);
        toolbar = view.findViewById(R.id.toolbar);
        shopCoverImageView = view.findViewById(R.id.shopCoverImageView);
        shopProfileImageView = view.findViewById(R.id.shopProfileImageView);
        shopNameTextView = view.findViewById(R.id.shopNameTextView);
        shopAddressTextView = view.findViewById(R.id.shopAddressTextView);
        ratingStarsLayout = view.findViewById(R.id.ratingStarsLayout);
        ratingTextView = view.findViewById(R.id.ratingTextView);
        reviewCountTextView = view.findViewById(R.id.reviewCountTextView);
        statusBadge = view.findViewById(R.id.statusBadge);
        callButton = view.findViewById(R.id.callButton);
        directionsButton = view.findViewById(R.id.directionsButton);
        detailsRecyclerView = view.findViewById(R.id.detailsRecyclerView);
        orderFab = view.findViewById(R.id.orderFab);
    }

    private void setupToolbar() {
        toolbar.setNavigationOnClickListener(v -> {
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });
    }

    private void setupRecyclerView() {
        Log.d(TAG, "Setting up RecyclerView for shop details");

        // Initialize enhanced adapter
        detailsAdapter = new ShopDetailsAdapter(requireContext(), this);

        // Setup RecyclerView with LinearLayoutManager for better performance
        LinearLayoutManager layoutManager = new LinearLayoutManager(requireContext());
        detailsRecyclerView.setLayoutManager(layoutManager);
        detailsRecyclerView.setAdapter(detailsAdapter);

        // Optimize RecyclerView performance
        detailsRecyclerView.setHasFixedSize(false);
        detailsRecyclerView.setItemViewCacheSize(20);
        detailsRecyclerView.setDrawingCacheEnabled(true);
        detailsRecyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        Log.d(TAG, "RecyclerView setup completed successfully");
    }

    private void setupClickListeners() {
        callButton.setOnClickListener(v -> {
            LaundryShopEntity shop = viewModel.getShop().getValue();
            if (shop != null && shop.getPhone() != null) {
                makePhoneCall(shop.getPhone());
            } else {
                ToastUtils.showWarning(requireContext(), "Phone number not available");
            }
        });

        directionsButton.setOnClickListener(v -> {
            LaundryShopEntity shop = viewModel.getShop().getValue();
            if (shop != null) {
                openDirections(shop.getLatitude(), shop.getLongitude());
            } else {
                ToastUtils.showWarning(requireContext(), "Location not available");
            }
        });

        orderFab.setOnClickListener(v -> {
            // Navigate to order placement
            openOrderPlacement();
        });
    }

    private void observeViewModel() {
        // Observe shop data
        viewModel.getShop().observe(getViewLifecycleOwner(), shop -> {
            Log.d(TAG, "observeViewModel: Shop data received - " + (shop != null ? shop.getName() : "null"));
            if (shop != null) {
                Log.d(TAG, "Updating shop UI for: " + shop.getName());
                updateShopUI(shop);
            } else {
                Log.w(TAG, "Shop data is null, not updating UI");
            }
        });

        // Observe services and items data for RecyclerView
        viewModel.getServices().observe(getViewLifecycleOwner(), services -> {
            Log.d(TAG, "Services data received - count: " + (services != null ? services.size() : "null"));
            updateRecyclerViewData();
        });

        viewModel.getItems().observe(getViewLifecycleOwner(), items -> {
            Log.d(TAG, "Items data received - count: " + (items != null ? items.size() : "null"));
            updateRecyclerViewData();
        });

        // Observe loading state
        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            // Show/hide loading indicator if needed
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                ToastUtils.showError(requireContext(), errorMessage);
                viewModel.clearErrorMessage();
            }
        });
    }

    private void updateRecyclerViewData() {
        if (detailsAdapter != null) {
            List<ShopServiceEntity> services = viewModel.getServices().getValue();
            List<ShopItemEntity> items = viewModel.getItems().getValue();
            detailsAdapter.updateData(services, items);
            Log.d(TAG, "RecyclerView data updated with " +
                  (services != null ? services.size() : 0) + " services and " +
                  (items != null ? items.size() : 0) + " items");
        }
    }

    private void updateShopUI(LaundryShopEntity shop) {
        Log.d(TAG, "updateShopUI() called for shop: " + shop.getName());

        try {
            // Set shop name in collapsing toolbar
            if (collapsingToolbar != null) {
                collapsingToolbar.setTitle(shop.getName());
                Log.d(TAG, "Set collapsing toolbar title: " + shop.getName());
            } else {
                Log.w(TAG, "collapsingToolbar is null");
            }

            // Load cover image
            if (shop.getCoverImageUrl() != null && !shop.getCoverImageUrl().isEmpty()) {
                Log.d(TAG, "Loading cover image: " + shop.getCoverImageUrl());
                Glide.with(this)
                        .load(shop.getCoverImageUrl())
                        .placeholder(R.drawable.shop_cover_placeholder)
                        .error(R.drawable.shop_cover_placeholder)
                        .into(shopCoverImageView);
            } else {
                Log.d(TAG, "No cover image URL, using placeholder");
                if (shopCoverImageView != null) {
                    shopCoverImageView.setImageResource(R.drawable.shop_cover_placeholder);
                }
            }

            // Load profile image
            if (shop.getProfileImageUrl() != null && !shop.getProfileImageUrl().isEmpty()) {
                Log.d(TAG, "Loading profile image: " + shop.getProfileImageUrl());
                Glide.with(this)
                        .load(shop.getProfileImageUrl())
                        .placeholder(R.drawable.ic_shop_placeholder)
                        .error(R.drawable.ic_shop_placeholder)
                        .into(shopProfileImageView);
            } else {
                Log.d(TAG, "No profile image URL, using placeholder");
                if (shopProfileImageView != null) {
                    shopProfileImageView.setImageResource(R.drawable.ic_shop_placeholder);
                }
            }

            // Set shop details
            if (shopNameTextView != null) {
                shopNameTextView.setText(shop.getName());
                shopNameTextView.setVisibility(View.VISIBLE);
                Log.d(TAG, "Set shop name: " + shop.getName());
            } else {
                Log.w(TAG, "shopNameTextView is null");
            }

            if (shopAddressTextView != null) {
                shopAddressTextView.setText(shop.getAddress());
                shopAddressTextView.setVisibility(View.VISIBLE);
                Log.d(TAG, "Set shop address: " + shop.getAddress());
            } else {
                Log.w(TAG, "shopAddressTextView is null");
            }

            if (ratingTextView != null) {
                ratingTextView.setText(viewModel.getFormattedRating());
                Log.d(TAG, "Set rating text: " + viewModel.getFormattedRating());
            } else {
                Log.w(TAG, "ratingTextView is null");
            }

            if (reviewCountTextView != null) {
                reviewCountTextView.setText(viewModel.getFormattedReviewCount());
                Log.d(TAG, "Set review count: " + viewModel.getFormattedReviewCount());
            } else {
                Log.w(TAG, "reviewCountTextView is null");
            }

            // Update rating stars
            updateRatingStars(shop.getRating());

            // Update status badge
            updateStatusBadge(shop);

            Log.d(TAG, "updateShopUI() completed successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error updating shop UI", e);
        }
    }

    private void updateRatingStars(double rating) {
        ratingStarsLayout.removeAllViews();

        int fullStars = (int) rating;
        boolean hasHalfStar = (rating - fullStars) >= 0.5;

        // Add full stars
        for (int i = 0; i < fullStars; i++) {
            ImageView star = createStarImageView(R.drawable.ic_star_filled);
            ratingStarsLayout.addView(star);
        }

        // Add half star if needed
        if (hasHalfStar) {
            ImageView star = createStarImageView(R.drawable.ic_star_half);
            ratingStarsLayout.addView(star);
        }

        // Add empty stars to make total of 5
        int totalStars = fullStars + (hasHalfStar ? 1 : 0);
        for (int i = totalStars; i < 5; i++) {
            ImageView star = createStarImageView(R.drawable.ic_star_empty);
            ratingStarsLayout.addView(star);
        }
    }

    private ImageView createStarImageView(int drawableRes) {
        ImageView star = new ImageView(requireContext());
        star.setImageResource(drawableRes);
        star.setColorFilter(getResources().getColor(R.color.rating_star, null));

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                getResources().getDimensionPixelSize(R.dimen.star_size),
                getResources().getDimensionPixelSize(R.dimen.star_size)
        );
        params.setMarginEnd(getResources().getDimensionPixelSize(R.dimen.star_margin));
        star.setLayoutParams(params);

        return star;
    }

    private void updateStatusBadge(LaundryShopEntity shop) {
        if (viewModel.isShopOpen()) {
            statusBadge.setText(R.string.open);
            statusBadge.setBackgroundResource(R.drawable.badge_open);
        } else {
            statusBadge.setText(R.string.closed);
            statusBadge.setBackgroundResource(R.drawable.badge_closed);
        }
    }

    private void makePhoneCall(String phoneNumber) {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        intent.setData(Uri.parse("tel:" + phoneNumber));
        startActivity(intent);
    }

    private void openDirections(double latitude, double longitude) {
        String uri = String.format("geo:%f,%f?q=%f,%f", latitude, longitude, latitude, longitude);
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
        intent.setPackage("com.google.android.apps.maps");

        if (intent.resolveActivity(requireActivity().getPackageManager()) != null) {
            startActivity(intent);
        } else {
            // Fallback to browser
            String url = String.format("https://www.google.com/maps?q=%f,%f", latitude, longitude);
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(browserIntent);
        }
    }

    private void openOrderPlacement() {
        LaundryShopEntity shop = viewModel.getShop().getValue();
        if (shop == null) {
            ToastUtils.showError(requireContext(), "Shop information not available");
            return;
        }

        // Navigate to checkout with shop context
        navigateToCheckoutWithShop(shop);
    }

    private void navigateToCheckoutWithShop(LaundryShopEntity shop) {
        try {
            // Create checkout fragment with shop context
            CheckoutFragment checkoutFragment = new CheckoutFragment();

            // Pass shop information to checkout
            Bundle args = new Bundle();
            args.putInt("shop_id", shop.getId());
            args.putString("shop_name", shop.getName());
            args.putString("shop_address", shop.getAddress());
            checkoutFragment.setArguments(args);

            // Navigate to checkout
            requireActivity().getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left,
                                       R.anim.slide_in_left, R.anim.slide_out_right)
                    .replace(R.id.fragment_container, checkoutFragment)
                    .addToBackStack(null)
                    .commit();

            Log.d(TAG, "Navigated to checkout for shop: " + shop.getName());
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to checkout", e);
            ToastUtils.showError(requireContext(), "Unable to proceed to checkout");
        }
    }

    // ShopDetailsAdapter.OnItemClickListener implementation
    @Override
    public void onServiceClick(ShopServiceEntity service) {
        Log.d(TAG, "Service clicked: " + service.getServiceName());
        ToastUtils.showInfo(requireContext(), "Service: " + service.getServiceName());
        // TODO: Navigate to service details or filter items by service
    }

    @Override
    public void onItemClick(ShopItemEntity item) {
        Log.d(TAG, "Item clicked: " + item.getItemName());
        ToastUtils.showInfo(requireContext(), "Item: " + item.getItemName());
        // TODO: Show item details dialog
    }

    @Override
    public void onAddToCartClick(ShopItemEntity item) {
        Log.d(TAG, "Add to cart clicked for: " + item.getItemName());
        ToastUtils.showSuccess(requireContext(), "Added " + item.getItemName() + " to cart");
        // TODO: Implement add to cart functionality
    }
}