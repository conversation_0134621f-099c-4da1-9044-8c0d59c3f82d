-- Simple Admin Payment Configuration Migration
-- This migration adds admin payment settings to ensure users pay to admin accounts

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS `admin_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL UNIQUE,
    `setting_value` text NOT NULL,
    `description` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert admin payment configuration settings
INSERT INTO `admin_settings` (`setting_key`, `setting_value`, `description`) VALUES
('admin_bkash_number', '***********', 'Admin bKash account number for receiving payments'),
('admin_nagad_number', '***********', 'Admin Nagad account number for receiving payments'),
('admin_rocket_number', '***********', 'Admin Rocket account number for receiving payments'),
('admin_payment_instructions', 'Please send payment to the admin account numbers provided. Include your order number in the transaction reference for faster processing.', 'Instructions shown to users during payment'),
('payment_verification_required', '1', 'Whether payment verification is required (1 = yes, 0 = no)'),
('admin_whatsapp', '+*************', 'Admin WhatsApp number for customer support'),
('delivery_fee', '50.00', 'Default delivery fee amount'),
('commission_rate', '15.00', 'Default commission rate percentage for shops'),
('app_maintenance_mode', '0', 'App maintenance mode (1 = enabled, 0 = disabled)'),
('app_version', '1.0.0', 'Current app version')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `description` = VALUES(`description`),
    `updated_at` = CURRENT_TIMESTAMP;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS `idx_admin_settings_key` ON `admin_settings` (`setting_key`);
