-- Enhanced Shops Management Database Migration
-- This migration adds all required tables for the enhanced admin shops management system
-- and payment withdrawal request functionality

-- 1. Shop Admin Notes Table
-- Stores administrative notes about shops for internal tracking
CREATE TABLE IF NOT EXISTS `shop_admin_notes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `shop_id` int(11) NOT NULL,
    `admin_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `note_type` enum('general', 'verification', 'warning', 'commission', 'payment') DEFAULT 'general',
    `is_important` tinyint(1) DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_admin_id` (`admin_id`),
    <PERSON>EY `idx_created_at` (`created_at`),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (`shop_id`) REFERENCES `laundry_shops` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Commission Logs Table
-- Tracks all commission rate changes with history
CREATE TABLE IF NOT EXISTS `commission_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `shop_id` int(11) NOT NULL,
    `old_rate` decimal(5,2) DEFAULT NULL,
    `new_rate` decimal(5,2) NOT NULL,
    `changed_by` int(11) NOT NULL,
    `change_reason` varchar(255) DEFAULT NULL,
    `effective_date` date NOT NULL,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_changed_by` (`changed_by`),
    KEY `idx_effective_date` (`effective_date`),
    FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`changed_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Admin Activity Logs Table
-- Comprehensive audit trail for all admin actions
CREATE TABLE IF NOT EXISTS `admin_activity_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `admin_id` int(11) NOT NULL,
    `action` varchar(100) NOT NULL,
    `description` text NOT NULL,
    `shop_id` int(11) DEFAULT NULL,
    `target_type` enum('shop', 'user', 'order', 'payment', 'system') DEFAULT 'shop',
    `target_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `additional_data` json DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_action` (`action`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_target` (`target_type`, `target_id`),
    FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Payment Withdrawal Requests Table
-- Main table for shop owner withdrawal requests
CREATE TABLE IF NOT EXISTS `payment_withdrawal_requests` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `shop_id` int(11) NOT NULL,
    `shop_owner_id` int(11) NOT NULL,
    `request_amount` decimal(10,2) NOT NULL,
    `available_balance` decimal(10,2) NOT NULL,
    `payment_method` enum('bkash', 'nagad', 'rocket', 'bank_transfer') NOT NULL,
    `account_type` enum('personal', 'agent', 'merchant') NOT NULL DEFAULT 'personal',
    `account_number` varchar(20) NOT NULL,
    `account_holder_name` varchar(100) NOT NULL,
    `shop_owner_notes` text DEFAULT NULL,
    `status` enum('pending', 'processing', 'approved', 'completed', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending',
    `admin_id` int(11) DEFAULT NULL,
    `admin_notes` text DEFAULT NULL,
    `rejection_reason` text DEFAULT NULL,
    `transaction_reference` varchar(100) DEFAULT NULL,
    `processing_fee` decimal(8,2) DEFAULT 0.00,
    `final_amount` decimal(10,2) DEFAULT NULL,
    `processed_at` timestamp NULL DEFAULT NULL,
    `completed_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_shop_owner_id` (`shop_owner_id`),
    KEY `idx_status` (`status`),
    KEY `idx_payment_method` (`payment_method`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_admin_id` (`admin_id`),
    FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`shop_owner_id`) REFERENCES `shop_owners` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Payment Withdrawal Request Status History Table
-- Tracks all status changes for audit trail
CREATE TABLE IF NOT EXISTS `payment_withdrawal_status_history` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `withdrawal_request_id` int(11) NOT NULL,
    `old_status` enum('pending', 'processing', 'approved', 'completed', 'rejected', 'cancelled') DEFAULT NULL,
    `new_status` enum('pending', 'processing', 'approved', 'completed', 'rejected', 'cancelled') NOT NULL,
    `changed_by` int(11) DEFAULT NULL,
    `change_reason` text DEFAULT NULL,
    `admin_notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_withdrawal_request_id` (`withdrawal_request_id`),
    KEY `idx_changed_by` (`changed_by`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`withdrawal_request_id`) REFERENCES `payment_withdrawal_requests` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`changed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Shop Owner Earnings Table
-- Tracks shop owner earnings and available balance
CREATE TABLE IF NOT EXISTS `shop_owner_earnings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `shop_id` int(11) NOT NULL,
    `shop_owner_id` int(11) NOT NULL,
    `total_earnings` decimal(12,2) NOT NULL DEFAULT 0.00,
    `withdrawn_amount` decimal(12,2) NOT NULL DEFAULT 0.00,
    `pending_withdrawal` decimal(12,2) NOT NULL DEFAULT 0.00,
    `available_balance` decimal(12,2) NOT NULL DEFAULT 0.00,
    `last_withdrawal_date` timestamp NULL DEFAULT NULL,
    `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_shop_owner` (`shop_id`, `shop_owner_id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_shop_owner_id` (`shop_owner_id`),
    FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`shop_owner_id`) REFERENCES `shop_owners` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. Payment Method Settings Table
-- Stores payment method configurations and fees
CREATE TABLE IF NOT EXISTS `payment_method_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `method_name` varchar(50) NOT NULL,
    `display_name` varchar(100) NOT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `min_amount` decimal(8,2) NOT NULL DEFAULT 0.00,
    `max_amount` decimal(10,2) NOT NULL DEFAULT 999999.99,
    `processing_fee_type` enum('fixed', 'percentage') NOT NULL DEFAULT 'percentage',
    `processing_fee_value` decimal(8,4) NOT NULL DEFAULT 0.00,
    `account_number_pattern` varchar(100) DEFAULT NULL,
    `account_number_example` varchar(50) DEFAULT NULL,
    `instructions` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_method_name` (`method_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default payment method settings
INSERT INTO `payment_method_settings` (`method_name`, `display_name`, `min_amount`, `max_amount`, `processing_fee_type`, `processing_fee_value`, `account_number_pattern`, `account_number_example`, `instructions`) VALUES
('bkash', 'bKash', 50.00, 25000.00, 'percentage', 1.85, '^01[3-9][0-9]{8}$', '***********', 'Enter your bKash account number (11 digits starting with 01)'),
('nagad', 'Nagad', 50.00, 25000.00, 'percentage', 1.99, '^01[3-9][0-9]{8}$', '***********', 'Enter your Nagad account number (11 digits starting with 01)'),
('rocket', 'Rocket', 50.00, 20000.00, 'percentage', 1.80, '^01[3-9][0-9]{8}$', '***********', 'Enter your Rocket account number (11 digits starting with 01)'),
('bank_transfer', 'Bank Transfer', 500.00, 100000.00, 'fixed', 25.00, NULL, 'Account Number', 'Bank transfers may take 1-3 business days to process');

-- Add indexes for better performance
CREATE INDEX idx_shop_admin_notes_shop_type ON shop_admin_notes(shop_id, note_type);
CREATE INDEX idx_commission_logs_shop_date ON commission_logs(shop_id, effective_date);
CREATE INDEX idx_admin_activity_target ON admin_activity_logs(target_type, target_id);
CREATE INDEX idx_withdrawal_requests_status_date ON payment_withdrawal_requests(status, created_at);
CREATE INDEX idx_withdrawal_history_request_date ON payment_withdrawal_status_history(withdrawal_request_id, created_at);

-- Add missing columns to existing tables if they don't exist
ALTER TABLE `laundry_shops` 
ADD COLUMN IF NOT EXISTS `commission_updated_at` timestamp NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `verified_at` timestamp NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `admin_notes` text DEFAULT NULL;

-- Update existing commission logs if commission changes exist
-- This will be handled by the application logic when commission updates occur

-- Create views for easier data access
CREATE OR REPLACE VIEW `shop_earnings_summary` AS
SELECT 
    ls.id as shop_id,
    ls.name as shop_name,
    ls.owner_name,
    soe.total_earnings,
    soe.withdrawn_amount,
    soe.pending_withdrawal,
    soe.available_balance,
    soe.last_withdrawal_date,
    COUNT(pwr.id) as total_withdrawal_requests,
    COUNT(CASE WHEN pwr.status = 'pending' THEN 1 END) as pending_requests,
    COUNT(CASE WHEN pwr.status = 'completed' THEN 1 END) as completed_requests
FROM laundry_shops ls
LEFT JOIN shop_owner_earnings soe ON ls.id = soe.shop_id
LEFT JOIN payment_withdrawal_requests pwr ON ls.id = pwr.shop_id
GROUP BY ls.id, ls.name, ls.owner_name, soe.total_earnings, soe.withdrawn_amount, soe.pending_withdrawal, soe.available_balance, soe.last_withdrawal_date;

-- Create triggers to automatically update earnings when orders are completed
DELIMITER //

CREATE TRIGGER IF NOT EXISTS `update_shop_earnings_on_order_completion` 
AFTER UPDATE ON `orders` 
FOR EACH ROW 
BEGIN
    IF NEW.status = 'delivered' AND OLD.status != 'delivered' THEN
        INSERT INTO shop_owner_earnings (shop_id, shop_owner_id, total_earnings, available_balance)
        SELECT 
            NEW.shop_id,
            so.id,
            NEW.shop_commission,
            NEW.shop_commission
        FROM shop_owners so 
        WHERE so.shop_id = NEW.shop_id
        ON DUPLICATE KEY UPDATE
            total_earnings = total_earnings + NEW.shop_commission,
            available_balance = available_balance + NEW.shop_commission;
    END IF;
END//

DELIMITER ;
