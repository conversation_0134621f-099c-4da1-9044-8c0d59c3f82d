<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/glass_item_background"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    android:elevation="8dp"
    android:animateLayoutChanges="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <!-- Compact Item Image with Enhanced Glass Effect -->
        <FrameLayout
            android:id="@+id/image_card"
            android:layout_width="0dp"
            android:layout_height="188dp"
            android:background="@drawable/glass_card_background"
            android:padding="3dp"
            android:elevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/item_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/item_image"
                android:scaleType="fitXY"
                android:background="@drawable/glass_section_background"
                tools:src="@drawable/placeholder_image" />

            <!-- Out of Stock Overlay with Glass Effect -->
            <FrameLayout
                android:id="@+id/out_of_stock_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#80000000"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/glass_dialog_background"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:rotation="-15"
                    android:text="@string/out_of_stock"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:elevation="6dp" />
            </FrameLayout>
        </FrameLayout>

        <!-- Compact Item Details with Clear Labels -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="6dp"
            android:padding="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image_card">

            <!-- Item Name Section with Enhanced Visibility -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/glass_section_background"
                android:padding="8dp"
                android:elevation="2dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Name:"
                    android:textColor="#E0E0E0"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-condensed"
                    android:layout_marginEnd="6dp" />

                <TextView
                    android:id="@+id/item_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-condensed"
                    android:background="@drawable/glass_section_background"
                    android:paddingStart="8dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="8dp"
                    android:paddingBottom="4dp"
                    android:elevation="1dp"
                    tools:text="Bed Sheet" />
            </LinearLayout>

            <!-- Item Description Section with Expandable Text -->
            <LinearLayout
                android:id="@+id/description_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="4dp"
                android:background="@drawable/glass_section_background"
                android:padding="8dp"
                android:elevation="1dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="top">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Desc:"
                        android:textColor="#FBF4F4"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-condensed"
                        android:layout_marginEnd="6dp"
                        android:layout_marginTop="1dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/item_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/background"
                            android:textSize="13sp"
                            android:fontFamily="sans-serif"
                            android:lineSpacingExtra="2dp"
                            android:background="@drawable/glass_section_background"
                            android:paddingStart="8dp"
                            android:paddingTop="6dp"
                            android:paddingEnd="8dp"
                            android:paddingBottom="6dp"
                            android:elevation="1dp"
                            tools:text="Single or double bed sheet washing service with premium care and attention to detail for your comfort" />

                        <TextView
                            android:id="@+id/expand_collapse_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:text="Show more"
                            android:textColor="#4FC3F7"
                            android:textSize="11sp"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif-condensed"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/glass_section_background"
                            android:elevation="1dp"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- Vertical Price and Add to Cart Section with Better Visibility -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="6dp"
                android:background="@drawable/glass_section_background"
                android:padding="8dp"
                android:elevation="3dp">

                <!-- Price Section with Enhanced Visibility -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="@drawable/glass_section_background"
                    android:padding="6dp"
                    android:elevation="2dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Price:"
                        android:textColor="#E0E0E0"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-condensed"
                        android:layout_marginEnd="6dp" />

                    <TextView
                        android:id="@+id/item_price"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="#FFFFFF"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-condensed"
                        android:background="@drawable/glass_section_background"
                        android:paddingStart="8dp"
                        android:paddingTop="4dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="4dp"
                        android:elevation="1dp"
                        android:gravity="center_vertical"
                        android:ellipsize="end"
                        android:maxLines="1"
                        tools:text="৳120" />
                </LinearLayout>

                <!-- Add to Cart Button Section with Text -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:gravity="center"
                    android:background="@drawable/glass_section_background"
                    android:padding="6dp"
                    android:elevation="2dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/add_to_cart_button"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:minWidth="120dp"
                        android:text="@string/add_to_cart_short"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-condensed"
                        android:backgroundTint="@color/home_accent_blue"
                        android:elevation="6dp"
                        app:cornerRadius="20dp"
                        app:icon="@drawable/ic_add"
                        app:iconTint="@android:color/white"
                        app:iconSize="14dp"
                        app:iconGravity="start"
                        app:iconPadding="4dp"
                        android:paddingStart="12dp"
                        android:paddingEnd="12dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:insetTop="0dp"
                        android:insetBottom="0dp"
                        android:insetLeft="0dp"
                        android:insetRight="0dp"
                        app:rippleColor="@android:color/white"
                        android:letterSpacing="0.05"
                        style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
