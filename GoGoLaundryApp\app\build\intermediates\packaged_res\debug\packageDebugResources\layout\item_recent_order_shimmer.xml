<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <!-- Status Indicator Shimmer -->
            <View
                android:id="@+id/status_indicator_shimmer"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:background="@drawable/shimmer_circle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Order Number Shimmer -->
            <View
                android:id="@+id/order_number_shimmer"
                android:layout_width="120dp"
                android:layout_height="16dp"
                android:background="@drawable/shimmer_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Order Date Shimmer -->
            <View
                android:id="@+id/order_date_shimmer"
                android:layout_width="80dp"
                android:layout_height="12dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/shimmer_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_number_shimmer" />

            <!-- Status Shimmer -->
            <View
                android:id="@+id/order_status_shimmer"
                android:layout_width="60dp"
                android:layout_height="12dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/shimmer_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_date_shimmer" />

            <!-- Order Items Shimmer -->
            <View
                android:id="@+id/order_items_shimmer"
                android:layout_width="100dp"
                android:layout_height="12dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/shimmer_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_status_shimmer" />

            <!-- Total Amount Shimmer -->
            <View
                android:id="@+id/order_total_shimmer"
                android:layout_width="80dp"
                android:layout_height="16dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/shimmer_rounded"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_items_shimmer" />

            <!-- Buttons Container Shimmer -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/order_total_shimmer">

                <!-- Receipt Button Shimmer -->
                <View
                    android:layout_width="60dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/shimmer_button_background" />

                <!-- Track Button Shimmer -->
                <View
                    android:layout_width="50dp"
                    android:layout_height="24dp"
                    android:background="@drawable/shimmer_button_background" />

            </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
