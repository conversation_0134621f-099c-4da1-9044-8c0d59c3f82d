<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@drawable/gradient_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Hero Section with Glass Effect -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/hero_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/glass_hero_background"
            android:elevation="8dp"
            android:paddingStart="24dp"
            android:paddingTop="6dp"
            android:paddingEnd="24dp"
            android:paddingBottom="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Welcome Section -->
            <TextView
                android:id="@+id/welcome_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Hello!"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline4"
                android:textStyle="bold"
                android:textColor="@color/home_text_on_gradient"
                android:letterSpacing="0.02"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/welcome_subtitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Ready for fresh laundry today?"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                android:textColor="@color/home_text_on_gradient"
                android:alpha="0.9"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/welcome_text" />



        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Content Section with Glass Effect -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/content_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/glass_content_background"
            android:elevation="6dp"
            android:paddingStart="24dp"
            android:paddingTop="32dp"
            android:paddingEnd="24dp"
            android:paddingBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/hero_section">



            <!-- Recent Orders Section -->
            <LinearLayout
                android:id="@+id/recent_orders_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_receipt"
                    android:layout_marginEnd="12dp"
                    app:tint="@color/home_accent_blue" />

                <TextView
                    android:id="@+id/recent_orders_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Recent Orders"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:background="@drawable/glass_section_background"
                    android:padding="12dp"
                    android:elevation="2dp" />

                <TextView
                    android:id="@+id/view_all_orders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="View All"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/home_accent_blue"
                    android:textStyle="bold"
                    android:padding="10dp"
                    android:background="@drawable/glass_button_background"
                    android:elevation="3dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Recent Orders Container with Glass Effect -->
            <FrameLayout
                android:id="@+id/recent_orders_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="6dp"
                app:layout_constraintTop_toBottomOf="@id/recent_orders_header">

                <!-- Recent Orders Shimmer Effect -->
                <include
                    android:id="@+id/recent_orders_shimmer"
                    layout="@layout/layout_recent_orders_shimmer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible" />

                <!-- Recent Orders RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recent_orders_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:nestedScrollingEnabled="false"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_recent_order" />

                <!-- Empty State for Recent Orders -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/recent_orders_empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/glass_empty_state_background"
                    android:elevation="2dp"
                    android:padding="32dp"
                    android:visibility="gone">

                    <!-- Empty State Icon with Glass Background -->
                    <FrameLayout
                        android:id="@+id/empty_icon_container"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:background="@drawable/glass_icon_background"
                        android:elevation="4dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_receipt"
                            android:alpha="0.7"
                            app:tint="@color/home_accent_blue" />

                    </FrameLayout>

                    <!-- Empty State Title -->
                    <TextView
                        android:id="@+id/empty_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="No Recent Orders"
                        android:textAlignment="center"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@android:color/white"
                        android:textStyle="bold"
                        android:background="@drawable/glass_section_background"
                        android:padding="8dp"
                        android:elevation="1dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/empty_icon_container" />

                    <!-- Empty State Subtitle -->
                    <TextView
                        android:id="@+id/empty_subtitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Start your first order to see it here"
                        android:textAlignment="center"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                        android:textColor="#E0E0E0"
                        android:background="@drawable/glass_section_background"
                        android:padding="6dp"
                        android:elevation="1dp"
                        android:lineSpacingExtra="2dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/empty_title" />

                    <!-- Call to Action Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_start_order"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:text="Start Order"
                        android:textColor="@android:color/white"
                        android:textStyle="bold"
                        android:backgroundTint="@color/home_accent_blue"
                        android:elevation="6dp"
                        app:cornerRadius="25dp"
                        app:icon="@drawable/ic_add"
                        app:iconTint="@android:color/white"
                        app:iconGravity="textStart"
                        app:iconPadding="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/empty_subtitle"
                        style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

            <!-- Popular Services Section -->
            <LinearLayout
                android:id="@+id/services_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_constraintTop_toBottomOf="@id/recent_orders_container">

                <TextView
                    android:id="@+id/popular_services_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Popular Services"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:background="@drawable/glass_section_background"
                    android:padding="12dp"
                    android:elevation="2dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="View All"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/home_accent_blue"
                    android:textStyle="bold"
                    android:padding="10dp"
                    android:background="@drawable/glass_button_background"
                    android:elevation="3dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Services Container with Glass Effect -->
            <FrameLayout
                android:id="@+id/services_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="16dp"
                app:layout_constraintTop_toBottomOf="@id/services_header">

                <!-- Services Shimmer Effect -->
                <include
                    android:id="@+id/services_shimmer"
                    layout="@layout/layout_services_shimmer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible" />

                <!-- Services RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/services_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="3"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_service_grid" />

            </FrameLayout>

            <!-- Popular Items Section -->
            <LinearLayout
                android:id="@+id/items_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_constraintTop_toBottomOf="@id/services_container">

                <TextView
                    android:id="@+id/popular_items_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Popular Items"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:background="@drawable/glass_section_background"
                    android:padding="12dp"
                    android:elevation="2dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="View All"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/home_accent_blue"
                    android:textStyle="bold"
                    android:padding="10dp"
                    android:background="@drawable/glass_button_background"
                    android:elevation="3dp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Popular Items Container with Glass Effect -->
            <FrameLayout
                android:id="@+id/popular_items_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="16dp"
                app:layout_constraintTop_toBottomOf="@id/items_header">

                <!-- Popular Items Shimmer Effect -->
                <include
                    android:id="@+id/popular_items_shimmer"
                    layout="@layout/layout_popular_items_shimmer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible" />

                <!-- Popular Items RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/popular_items_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:nestedScrollingEnabled="false"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_laundry_item" />

            </FrameLayout>

            <!-- Customer Reviews Section -->
            <LinearLayout
                android:id="@+id/reviews_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                app:layout_constraintTop_toBottomOf="@id/popular_items_container">

                <TextView
                    android:id="@+id/customer_reviews_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Customer Reviews"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:background="@drawable/glass_section_background"
                    android:padding="12dp"
                    android:elevation="2dp" />

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/glass_icon_background"
                    android:elevation="3dp"
                    android:padding="8dp"
                    android:layout_marginStart="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/home_accent_blue" />

                </FrameLayout>

            </LinearLayout>

            <!-- Reviews Container with Glass Effect -->
            <FrameLayout
                android:id="@+id/reviews_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:background="@drawable/glass_section_background"
                android:elevation="4dp"
                android:padding="16dp"
                app:layout_constraintTop_toBottomOf="@id/reviews_header"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/reviews_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_review" />

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
