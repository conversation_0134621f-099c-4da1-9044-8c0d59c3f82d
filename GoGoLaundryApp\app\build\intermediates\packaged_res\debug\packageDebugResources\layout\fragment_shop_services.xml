<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Services RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/servicesRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        tools:listitem="@layout/item_shop_service" />

    <!-- Empty State -->
    <TextView
        android:id="@+id/emptyTextView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="@string/no_services_found"
        android:textColor="@color/background_dark_new"
        android:textSize="16sp"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>
