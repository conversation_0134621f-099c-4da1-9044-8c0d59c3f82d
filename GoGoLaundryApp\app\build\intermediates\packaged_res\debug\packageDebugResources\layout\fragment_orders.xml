<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background">

    <!-- <PERSON>er -->
    <FrameLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/glass_hero_background"
        android:elevation="8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/margin_medium"
            android:paddingEnd="@dimen/margin_medium"
            android:paddingTop="@dimen/margin_medium"
            android:paddingBottom="@dimen/margin_medium">

            <TextView
                android:id="@+id/orders_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/my_orders"
                android:textSize="@dimen/text_size_headline"
                android:textColor="@color/white"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/orders_subtitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_extra_small"
                android:text="@string/orders_subtitle"
                android:textSize="@dimen/text_size_small"
                android:textColor="@color/white"
                android:alpha="0.8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/orders_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <!-- Glass Content Container -->
    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/glass_content_background"
        android:elevation="6dp"
        android:padding="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/orders_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_small"
                android:clipToPadding="false"
                android:scrollbarStyle="outsideOverlay"
                android:fadeScrollbars="true"
                android:scrollbars="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_order_grid" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </FrameLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:indeterminateTint="@color/primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Beautiful Empty State for Orders -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/empty_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:background="@drawable/glass_empty_state_background"
        android:elevation="8dp"
        android:padding="40dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Empty State Icon with Glass Background -->
        <FrameLayout
            android:id="@+id/empty_icon_container"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:background="@drawable/glass_icon_background"
            android:elevation="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_receipt"
                android:alpha="0.7"
                app:tint="@color/home_accent_blue" />

        </FrameLayout>

        <!-- Empty State Title -->
        <TextView
            android:id="@+id/empty_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="No Orders Yet"
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:background="@drawable/glass_section_background"
            android:padding="12dp"
            android:elevation="3dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/empty_icon_container" />

        <!-- Empty State Subtitle -->
        <TextView
            android:id="@+id/empty_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Start your laundry journey today!\nPlace your first order and experience our premium service."
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
            android:textColor="#E0E0E0"
            android:background="@drawable/glass_section_background"
            android:padding="12dp"
            android:elevation="2dp"
            android:lineSpacingExtra="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/empty_title" />

        <!-- Action Buttons Container -->
        <LinearLayout
            android:id="@+id/buttons_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/empty_subtitle">

            <!-- Primary Action Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_start_first_order"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:text="Start Your First Order"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:textSize="16sp"
                android:backgroundTint="@color/home_accent_blue"
                android:elevation="8dp"
                app:cornerRadius="28dp"
                app:icon="@drawable/ic_add"
                app:iconTint="@android:color/white"
                app:iconGravity="textStart"
                app:iconSize="20dp"
                app:iconPadding="12dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

            <!-- Secondary Action Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_browse_services"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="16dp"
                android:text="Browse Services"
                android:textColor="@color/home_accent_blue"
                android:textStyle="bold"
                android:textSize="14sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                app:strokeColor="@color/home_accent_blue"
                app:strokeWidth="2dp"
                app:cornerRadius="24dp"
                app:icon="@drawable/ic_dashboard"
                app:iconTint="@color/home_accent_blue"
                app:iconGravity="textStart"
                app:iconSize="16dp"
                app:iconPadding="8dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
